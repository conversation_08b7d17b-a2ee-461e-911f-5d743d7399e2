# ENV
NEXT_PUBLIC_ENV=
NEXT_PUBLIC_APP_VERSION=

# MICROFRONT
NEXT_PUBLIC_BASE_PATH=
NEXTAUTH_URL=
NEXT_PUBLIC_APP_URL=
PP_AUTH_LIB_AZURE_REDIRECT_URI=

# AUTH
NEXT_PUBLIC_AUTH_API_URL=
NEXT_PUBLIC_PEOPLE_PLATFORM_API_URL=
NEXT_PUBLIC_PEOPLE_PLATFORM_PHOTO_API_URL=
NEXT_PUBLIC_AUTH_API_URL_INTERNAL=
NEXTAUTH_SECRET=

# METRICS
NEXT_PUBLIC_DATADOG_APPLICATION_ID=
NEXT_PUBLIC_DATADOG_CLIENT_TOKEN=
NEXT_PUBLIC_GA_ID=

# TRANSLATIONS
NEXT_PUBLIC_TOLGEE_API_KEY=
NEXT_PUBLIC_TOLGEE_API_URL=
NEXT_PUBLIC_TOLGEE_CONTENT_DELIVERY_URL=

# PLATFORM_APPS
NEXT_PUBLIC_CHEERSHUB_URL=
NEXT_PUBLIC_MY_COMPENSATION_URL=
NEXT_PUBLIC_MY_TALENT_CARD_URL=
NEXT_PUBLIC_LCM_URL=
NEXT_PUBLIC_WORKDAY_URL=

# PP AUTH LIB
PP_AUTH_LIB_REDIS_URL=
PP_AUTH_LIB_AZURE_CLIENT_ID=
PP_AUTH_LIB_AZURE_CLIENT_SECRET=
NEXT_PUBLIC_PP_AUTH_LIB_AZURE_TENANT_ID=
PP_AUTH_LIB_AZURE_DEBUG=
PP_AUTH_LIB_AZURE_HTTP_TIMEOUT=
AUTH_API_URL_INTERNAL=
PP_AUTH_LIB_SYSTEM_ENUM=

# APIS
NEXT_PUBLIC_API_URL=
NEXT_PUBLIC_NORTHSTAR_AUTH_API_URL=
NEXT_PUBLIC_NORTHSTAR_USER_API_URL=

# EDITOR
REACT_EDITOR=
