/* eslint-disable no-console */
import chalk from 'chalk';

export class Logger {
  private name: string;

  constructor(name: string) {
    this.name = name;
  }

  private formatLog(content: unknown) {
    return JSON.stringify(content, null, 2);
  }

  private buildLogMessage(content: unknown) {
    return `[${this.name}] - ${new Date().toLocaleString()} ${this.formatLog(
      content,
    )}`;
  }

  log(content: unknown) {
    console.log(chalk.green(this.buildLogMessage(content)));
  }

  error(content: unknown) {
    console.error(chalk.red(this.buildLogMessage(content)));
  }

  info(content: unknown) {
    console.info(chalk.blue(this.buildLogMessage(content)));
  }

  warn(content: unknown) {
    console.warn(chalk.yellow(this.buildLogMessage(content)));
  }
}
