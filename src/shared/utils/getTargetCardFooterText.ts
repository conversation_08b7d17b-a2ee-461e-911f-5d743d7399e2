import { Target } from '../types/Target';
import { ProposalStatusEnum } from '../utils/enums';
import { TargetTypeEnum } from '../utils/enums/target-type';

export function getTargetCardFooterText(
  proposalStatus?: ProposalStatusEnum,
  currentTargetType?: TargetTypeEnum,
  targetData?: Target,
): string {
  if (!proposalStatus || !currentTargetType || !targetData?.targetTypes) {
    return '';
  }

  const hasProposal = targetData.targetTypes.some(
    t => t.type === TargetTypeEnum.PROPOSAL,
  );
  const hasFeedback = targetData.targetTypes.some(
    t => t.type === TargetTypeEnum.FEEDBACK,
  );
  const hasFinal = targetData.targetTypes.some(
    t => t.type === TargetTypeEnum.FINAL,
  );

  const isOnlyProposal = hasProposal && !hasFeedback && !hasFinal;
  const isProposalFeedback = hasProposal && hasFeedback && !hasFinal;
  const isAllThreeTypes = hasProposal && hasFeedback && hasFinal;

  switch (proposalStatus) {
    case ProposalStatusEnum.NOT_STARTED:
      return '';

    case ProposalStatusEnum.IN_PROGRESS_PROPOSAL:
      // No badges shown during proposal phase
      return '';

    case ProposalStatusEnum.IN_PROGRESS_FEEDBACK:
      if (currentTargetType === TargetTypeEnum.PROPOSAL) {
        return '';
      }
      return '';

    case ProposalStatusEnum.IN_PROGRESS_FINAL:
      if (currentTargetType === TargetTypeEnum.PROPOSAL) {
        if (isProposalFeedback || isAllThreeTypes) {
          return 'I Agree';
        }
        if (isOnlyProposal) {
          return "Don't agree";
        }
      }
      if (currentTargetType === TargetTypeEnum.FEEDBACK) {
        if (isAllThreeTypes || isProposalFeedback) {
          return 'I Agree';
        }
      }
      return '';

    case ProposalStatusEnum.COMPLETED:
      if (currentTargetType === TargetTypeEnum.PROPOSAL) {
        if (isProposalFeedback || isAllThreeTypes) {
          return 'I Agree';
        }
        if (isOnlyProposal) {
          return "Don't agree";
        }
      }
      if (currentTargetType === TargetTypeEnum.FEEDBACK) {
        if (isAllThreeTypes) {
          return 'I Agree';
        }
        if (isProposalFeedback) {
          return "Don't agree";
        }
      }
      if (currentTargetType === TargetTypeEnum.FINAL) {
        if (isAllThreeTypes) {
          return 'I Agree';
        }
      }
      return '';

    default:
      return '';
  }
}
