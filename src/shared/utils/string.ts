import {
  DEFAULT_LANGUAGE,
  Language,
  REGISTERED_LANGUAGES,
} from '../constants/i18n';

export function capitalize(str: string) {
  return `${str[0].toUpperCase()}${str.slice(1)}`;
}

const isValidLanguage = (languageId: string): languageId is Language => {
  return REGISTERED_LANGUAGES.includes(languageId as Language);
};

export function normalizeLanguage(language?: string) {
  let normalizedLanguage = '';

  if (typeof language === 'string') {
    normalizedLanguage = language.replace('_', '-');
  }
  if (normalizedLanguage && isValidLanguage(normalizedLanguage)) {
    return normalizedLanguage;
  }

  return DEFAULT_LANGUAGE;
}

/**
 * Remove invisible/zero-width characters and trim whitespace from string
 * @param str - String to clean
 * @returns Cleaned string without invisible characters and extra whitespace
 */
export function cleanInvisibleCharacters(
  str: string | number | null | undefined,
): string {
  return String(str || '')
    .replace(/[\u200B-\u200D\uFEFF]/g, '')
    .trim();
}
