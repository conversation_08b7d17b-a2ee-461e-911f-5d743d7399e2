export type DownloadFileResponse = {
  fileName: string;
  data: Blob;
};

export function getDownloadedFileName(contentDisposition: string): string {
  const fileNameMatch = contentDisposition?.match(/filename=([^;]+)/) || [];
  const decodedFileName = decodeURI(fileNameMatch[1]);

  return decodedFileName;
}

export function downloadBlob({ data, fileName }: DownloadFileResponse) {
  const url = window.URL.createObjectURL(new Blob([data]));
  const linkToFile = document.createElement('a');

  linkToFile.href = url;
  linkToFile.download = fileName;

  document.body.appendChild(linkToFile);
  linkToFile.click();

  window.URL.revokeObjectURL(url);
  document.body.removeChild(linkToFile);
}
