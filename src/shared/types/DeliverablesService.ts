import { DeliverableItem } from './Deliverable';
import { GetDeliverableFilters } from './GetDeliverableFilters';

export type DeliverableItemsResponse = {
  data: DeliverableItem[];
  pageNumber: number;
  pageSize: number;
  totalRecords: number;
};

export interface IDeliverablesService {
  getDeliverables(
    filters: GetDeliverableFilters,
  ): Promise<DeliverableItemsResponse>;
  getDeliverableById(uid: string): Promise<DeliverableItem>;
}
