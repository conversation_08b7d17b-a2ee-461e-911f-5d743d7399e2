import { FilterParams } from './FilterParams';
import { Proposal } from './Proposal';

export type ProposalFilters = FilterParams & {
  zones?: string[];
  status?: string[];
  sltLevel?: string[];
  sltName?: string[];
  businessFunctions?: string[];
};

export type ProposalItemsResponse = {
  data: Proposal[];
  pageNumber: number;
  pageSize: number;
  totalRecords: number;
};

export type ProposalItemResponse = Proposal[];

export interface ProposalFiltersResponse {
  data: {
    status: { label: string; value: string }[];
    zones: { label: string; value: string }[];
    functions: { label: string; value: string }[];
    sltLevels: { label: string; value: string }[];
    sltNames: { label: string; value: string }[];
  };
}

export interface IProposalService {
  getProposals(filters: ProposalFilters): Promise<ProposalItemsResponse>;
  getFilters(): Promise<ProposalFiltersResponse>;
}
