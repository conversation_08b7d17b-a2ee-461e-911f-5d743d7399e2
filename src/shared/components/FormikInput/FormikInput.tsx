import React from 'react';
import { Container, Input, Label, Typography } from '@ghq-abi/design-system-v2';
import { ErrorMessage, useField } from 'formik';

import { cn } from '~/shared/utils/cn';

export function FormikInput({
  name,
  label,
  placeholder,
  onChange,
  value,
  ...props
}: FormikInputProps) {
  const [_, meta] = useField(name);
  const hasError = meta.touched && meta.error;

  return (
    <div className="grid w-full items-center">
      <Label className="pb-2" htmlFor={name}>
        <Typography
          variant="body-sm-bold"
          className="font-semibold"
          color="light"
        >
          {label}
        </Typography>
      </Label>
      <Input
        id={name}
        name={name}
        placeholder={placeholder}
        onChange={onChange}
        value={value}
        className={cn(
          'bg-gray-50',
          hasError ? 'border-red-500 focus:border-red-500' : '',
        )}
        {...props}
      />
      <Container className="min-h-[20px]">
        <ErrorMessage
          name={name}
          component="div"
          className="text-red-500 text-sm"
        />
      </Container>
    </div>
  );
}
