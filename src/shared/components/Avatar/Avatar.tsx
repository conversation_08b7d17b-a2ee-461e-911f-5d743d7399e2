import * as React from 'react';
import { Avatar as DSAvatar } from '@ghq-abi/design-system-v2';

import { cn } from '~/shared/utils/cn';

import { useGetAvatar } from './useAvatar';

export function Avatar({
  globalId,
  name,
  className,
  size = 40,
  fontScale = 0.4,
}: AvatarProps) {
  const { imageSrc, fallbackName } = useGetAvatar(globalId ?? '', name ?? '');

  return (
    <DSAvatar.Root
      className={cn(
        'rounded-full shrink-0 flex items-center justify-center',
        className,
      )}
      style={{ width: size, height: size }}
    >
      <DSAvatar.Image src={imageSrc || undefined} />
      <DSAvatar.Fallback style={{ fontSize: size * fontScale }}>
        {fallbackName}
      </DSAvatar.Fallback>
    </DSAvatar.Root>
  );
}
