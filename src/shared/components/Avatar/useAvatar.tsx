import { useMemo } from 'react';
import { useQuery } from '@tanstack/react-query';

import { peoplePlatformPhotoApi } from '~/shared/services/api';

async function getAvatar(globalId?: string) {
  try {
    const { data } = await peoplePlatformPhotoApi().get<Blob>(
      `/thumb/${globalId}`,
    );

    return data;
  } catch {
    return null;
  }
}

export function useGetAvatar(globalId: string, name: string) {
  let fallbackName = '';
  if (name) {
    const parts = name.trim().split(/\s+/);
    if (parts.length === 1) {
      fallbackName = parts[0].slice(0, 2).toUpperCase();
    } else {
      fallbackName = (parts[0][0] + parts[parts.length - 1][0]).toUpperCase();
    }
  }

  const query = useQuery({
    queryKey: ['getAvatar', globalId],
    queryFn: () => getAvatar(globalId),
    staleTime: Infinity,
    cacheTime: Infinity,
    retry: false,
    retryOnMount: false,
    refetchOnWindowFocus: false,
    refetchOnMount: false,
  });

  const imageSrc = useMemo(
    () => (query?.data ? URL.createObjectURL(query.data) : null),
    [query.data],
  );
  return Object.assign(query, {
    imageSrc,
    fallbackName,
  });
}
