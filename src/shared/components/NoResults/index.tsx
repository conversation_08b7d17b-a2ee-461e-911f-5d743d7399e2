import { NoDataCircleIcon } from '@ghq-abi/design-system-icons';
import { IconWrapper, Typography } from '@ghq-abi/design-system-v2';

import { cn } from '~/shared/utils/cn';

export interface NoResultsProps {
  title?: string;
  description?: string;
  icon?: React.ReactNode;
  className?: string;
}

export const NoResults = ({
  title,
  description,
  icon,
  className,
}: NoResultsProps) => {
  return (
    <div
      className={cn(
        'flex flex-col items-center justify-center min-h-[141px] gap-2 bg-[#FBFAFC] rounded-[4px] px-4 py-6',
        className,
      )}
    >
      {icon ? (
        <IconWrapper variant="primary" round="full" size={24}>
          {icon}
        </IconWrapper>
      ) : (
        <IconWrapper variant="default" round="full" size={48}>
          <NoDataCircleIcon className="h-12 w-12" />
        </IconWrapper>
      )}
      <Typography variant="body-md-bold">
        {title || 'No results yet'}
      </Typography>

      <Typography variant="metadata-sm-regular">
        {description || 'Change search employees in the search above'}
      </Typography>
    </div>
  );
};
