import React from 'react';
import { Check, X } from 'react-bootstrap-icons';
import { Button, Container, Typography } from '@ghq-abi/design-system-v2';

interface AgreementSelectionProps {
  value: boolean | null;
  onChange: (value: boolean) => void;
  disabled?: boolean;
  className?: string;
}

export function AgreementSelection({
  value,
  onChange,
  disabled = false,
  className = '',
}: AgreementSelectionProps) {
  return (
    <Container className={`flex flex-col gap-3 ${className}`}>
      <Typography variant="body-sm-bold" className="text-gray-900">
        Agreement Selection
      </Typography>
      <Container className="flex gap-3">
        <Button
          variant={value === false ? 'primary' : 'secondary'}
          size="sm"
          border="default"
          round="md"
          iconLeft={<X />}
          onClick={() => onChange(false)}
          disabled={disabled}
          className={`flex-1 ${
            value === false
              ? 'border-red-500 bg-red-50 text-red-700 hover:bg-red-100'
              : 'border-gray-300 text-gray-700 hover:bg-gray-50'
          }`}
        >
          <Typography variant="body-sm-medium" className="font-semibold">
            Don&apos;t Agree
          </Typography>
        </Button>
        <Button
          variant={value === true ? 'primary' : 'secondary'}
          size="sm"
          border="default"
          round="md"
          iconLeft={<Check />}
          onClick={() => onChange(true)}
          disabled={disabled}
          className={`flex-1 ${
            value === true
              ? 'border-green-500 bg-green-50 text-green-700 hover:bg-green-100'
              : 'border-gray-300 text-gray-700 hover:bg-gray-50'
          }`}
        >
          <Typography variant="body-sm-medium" className="font-semibold">
            I Agree
          </Typography>
        </Button>
      </Container>
    </Container>
  );
}
