import React from 'react';
import { Container, DateInput, Input, Label } from '@ghq-abi/design-system-v2';
import { ErrorMessage, useField } from 'formik';

import { cn } from '~/shared/utils/cn';

import { FormikDateProps } from './types';

export function FormikDate({
  name,
  label,
  placeholder,
  onChange,
  value,
  ...props
}: FormikDateProps) {
  const [field, meta, helpers] = useField(name);
  const hasError = meta.touched && meta.error;

  const dateValue = field.value ? new Date(field.value) : null;

  const handleDateChange = (date: Date | null) => {
    const dateValue = date ? date.toISOString().split('T')[0] : '';
    void helpers.setValue(dateValue);
    if (onChange) {
      onChange({ target: { value: dateValue } } as any);
    }
  };

  return (
    <div className="grid w-full items-center">
      <Label className="pb-1" htmlFor={name}>
        {label}
      </Label>
      <DateInput
        value={dateValue}
        onChange={handleDateChange}
        placeholder={placeholder}
        className={cn(
          'bg-gray-50 block text-neutral-500 border-neutral-200',
          hasError ? 'border-red-500 focus:border-red-500' : '',
        )}
        {...props}
      />
      <Container className="min-h-[20px]">
        <ErrorMessage
          name={name}
          component="div"
          className="text-red-500 text-sm"
        />
      </Container>
    </div>
  );
}
