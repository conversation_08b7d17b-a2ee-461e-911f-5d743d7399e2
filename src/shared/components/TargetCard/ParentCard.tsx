import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Trash } from 'react-bootstrap-icons';
import {
  <PERSON><PERSON>,
  Card,
  Container,
  IconWrapper,
  Typography,
} from '@ghq-abi/design-system-v2';

import { Target } from '~/shared/types/Target';
import { cn } from '~/shared/utils/cn';

import { Scale } from '../icons';

import { TruncatedTitle } from './shared';

interface ParentCardProps {
  data: Target;
  onRemoveActionClick?: (target: Target[]) => void;
  isDrawer?: boolean;
  isOnCatalogWithTabs?: boolean;
}

export function ParentCard({
  data,
  onRemoveActionClick,
  isDrawer,
  isOnCatalogWithTabs = false,
}: ParentCardProps) {
  const hasChildren = data.children && data.children.length > 0;
  const hasDirectDeliverable = !hasChildren && data.deliverable;

  const isKPI = hasDirectDeliverable
    ? data.deliverable?.deliverableType?.code === 'KPI'
    : data.children?.some(
        child => child.deliverable?.deliverableType?.code === 'KPI',
      );

  const icon = isKPI ? <BarChartFill size={24} /> : <ListCheck size={24} />;
  const iconVariant = isKPI ? 'secondary' : 'primary';

  const displayName = hasDirectDeliverable
    ? data.deliverable?.name || ''
    : data.children?.map(child => child.deliverable?.name).join(' + ') || '';

  const totalUsage = hasDirectDeliverable
    ? data.deliverable?.usage || 0
    : data.children
        ?.map(child => child.deliverable?.usage)
        .reduce((a, b) => (a || 0) + (b || 0), 0) || 0;

  const totalWeight = hasDirectDeliverable
    ? data.weight || 0
    : data.children
        ?.map(child => child.weight)
        .reduce((a, b) => (a || 0) + (b || 0), 0) || 0;

  return (
    <Card.Header className="flex flex-row items-center pb-1">
      <Card.Title className="flex flex-row gap-4 flex-1">
        <div className="flex-shrink-0">
          <IconWrapper variant={iconVariant} round="md" size={42}>
            {icon}
          </IconWrapper>
        </div>
        <Container
          className={cn('flex flex-col max-w-4xl', {
            'max-w-xs': isDrawer,
          })}
        >
          {isOnCatalogWithTabs ? (
            <TruncatedTitle title={displayName} maxLength={30} />
          ) : (
            <Typography
              variant="body-md-regular"
              color="dark"
              className="font-semibold whitespace-nowrap overflow-hidden text-ellipsis"
            >
              {displayName}
            </Typography>
          )}
          <Container className="flex flex-row items-center gap-4">
            <Typography
              variant="body-sm-regular"
              color="dark"
              className="flex flex-row items-center gap-1"
            >
              <Person />
              {totalUsage}
            </Typography>
            <Typography
              variant="body-sm-regular"
              color="dark"
              className="flex flex-row items-center gap-1"
            >
              <Scale />
              {totalWeight}%
            </Typography>
          </Container>
        </Container>
      </Card.Title>
      {onRemoveActionClick && (
        <div className="flex items-start">
          <Button
            variant="tertiary"
            size="icon"
            onClick={() => onRemoveActionClick([data])}
          >
            <Trash />
          </Button>
        </div>
      )}
    </Card.Header>
  );
}
