import { CheckCircle } from 'react-bootstrap-icons';
import { Button, Typography } from '@ghq-abi/design-system-v2';

interface AcceptTargetButtonProps {
  isAccepted?: boolean;
  isLoading?: boolean;
  onAccept: () => void;
}

export function AcceptTargetButton({
  isAccepted,
  isLoading,
  onAccept,
}: AcceptTargetButtonProps) {
  return (
    <Button
      variant="secondary"
      size="sm"
      border="default"
      round="md"
      iconLeft={<CheckCircle />}
      onClick={onAccept}
      disabled={isAccepted || isLoading}
      isLoading={isLoading}
    >
      <Typography variant="body-sm-medium" className="font-semibold">
        I Agree
      </Typography>
    </Button>
  );
}
