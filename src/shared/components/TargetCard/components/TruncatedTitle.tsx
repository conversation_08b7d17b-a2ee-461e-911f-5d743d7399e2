import { Tooltip, Typography } from '@ghq-abi/design-system-v2';

interface TruncatedTitleProps {
  title: string;
  maxLength?: number;
}

export function TruncatedTitle({ title, maxLength }: TruncatedTitleProps) {
  const truncatedTitle =
    maxLength && title.length > maxLength
      ? title.substring(0, maxLength) + '...'
      : title;

  return (
    <Tooltip.Provider>
      <Tooltip.Root>
        <Tooltip.Trigger className="w-56 truncate text-start">
          <Typography
            variant="body-md-regular"
            className="font-semibold whitespace-nowrap overflow-hidden text-ellipsis"
          >
            {truncatedTitle}
          </Typography>
        </Tooltip.Trigger>
        <Tooltip.Content
          className="z-[9999] break-words max-w-sm"
          side="bottom"
          align="start"
        >
          <Typography variant="body-sm-bold">{title}</Typography>
        </Tooltip.Content>
      </Tooltip.Root>
    </Tooltip.Provider>
  );
}
