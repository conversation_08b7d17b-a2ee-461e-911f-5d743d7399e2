import { Badge } from '@ghq-abi/design-system-v2';

interface TargetBadgesProps {
  shouldShowIAgree?: boolean;
  shouldShowDontAgree?: boolean;
}

export function TargetBadges({
  shouldShowIAgree,
  shouldShowDontAgree,
}: TargetBadgesProps) {
  if (!shouldShowIAgree && !shouldShowDontAgree) {
    return null;
  }

  return (
    <div className="flex gap-2">
      {shouldShowIAgree && (
        <Badge className="border border-green-500 text-green-500 bg-transparent">
          I Agree
        </Badge>
      )}
      {shouldShowDontAgree && (
        <Badge className="border border-red-500 text-red-500 bg-transparent">
          Don&apos;t Agree
        </Badge>
      )}
    </div>
  );
}
