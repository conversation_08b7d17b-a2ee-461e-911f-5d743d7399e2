import { Target } from '~/shared/types/Target';

import { useTargetActions } from '../hooks/useTargetActions';

import { AcceptTargetButton } from './AcceptTargetButton';
import { TargetBadges } from './TargetBadges';

interface TargetFooterProps {
  target: Target;
  isOnCatalogWithTabs?: boolean;
  showBadgesInProposalTab?: boolean;
  onAcceptTarget?: (targetUid: string) => void;
  isTargetAccepted?: (targetUid: string) => boolean;
  isTargetLoading?: (targetUid: string) => boolean;
}

export function TargetFooter({
  target,
  isOnCatalogWithTabs = false,
  showBadgesInProposalTab = false,
  onAcceptTarget,
  isTargetAccepted,
  isTargetLoading,
}: TargetFooterProps) {
  const {
    shouldShowIAgree,
    shouldShowDontAgree,
    shouldShowBadges,
    shouldShowAcceptButton,
    isAccepted,
    isLoading,
    handleAcceptTarget,
  } = useTargetActions({
    target,
    isOnCatalogWithTabs,
    showBadgesInProposalTab,
    onAcceptTarget,
    isTargetAccepted,
    isTargetLoading,
  });

  if (!isOnCatalogWithTabs) {
    return null;
  }

  const badges = shouldShowBadges ? (
    <TargetBadges
      shouldShowIAgree={shouldShowIAgree}
      shouldShowDontAgree={shouldShowDontAgree}
    />
  ) : null;

  const button = shouldShowAcceptButton ? (
    <AcceptTargetButton
      isAccepted={isAccepted}
      isLoading={isLoading}
      onAccept={handleAcceptTarget}
    />
  ) : null;

  if (badges || button) {
    return (
      <div className="flex justify-end items-center gap-2">
        {badges}
        {button}
      </div>
    );
  }

  return null;
}
