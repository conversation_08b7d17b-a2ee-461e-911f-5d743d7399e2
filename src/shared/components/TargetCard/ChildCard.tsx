import {
  Arrow<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  Trash,
} from 'react-bootstrap-icons';
import { useDraggable } from '@dnd-kit/core';
import { CSS } from '@dnd-kit/utilities';
import {
  <PERSON><PERSON>,
  Card,
  Container,
  IconWrapper,
  Typography,
} from '@ghq-abi/design-system-v2';

import { DeliverableTypeEnum } from '~/shared/types/Deliverable';
import { Target } from '~/shared/types/Target';
import { cn } from '~/shared/utils/cn';

import { Scale } from '../icons';

import { TargetFooter, TruncatedTitle } from './shared';

interface ChildCardProps {
  target: Target;
  disableDrag?: boolean;
  onEditTarget?: (target: Target) => void;
  onRemoveActionClick?: (target: Target[]) => void;
  hasManagerPermission?: boolean;
  hasEmployeePermission?: boolean;
  isDrawer?: boolean;
  showActions?: boolean;
  isOnCatalogWithTabs?: boolean;
  isInsideTargetCard?: boolean;
  disableChildActions?: boolean;
  onAcceptTarget?: (targetUid: string) => void;
  isTargetAccepted?: (targetUid: string) => boolean;
  isTargetLoading?: (targetUid: string) => boolean;
  showBadgesInProposalTab?: boolean;
}

export function ChildCard({
  target,
  disableDrag,
  onRemoveActionClick,
  onEditTarget,
  hasManagerPermission,
  isDrawer,
  showActions = false,
  isOnCatalogWithTabs = false,
  isInsideTargetCard = false,
  disableChildActions = false,
  onAcceptTarget,
  isTargetAccepted,
  isTargetLoading,
  showBadgesInProposalTab = false,
}: ChildCardProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    isDragging: dragging,
  } = useDraggable({
    id: target?.uid ? target.uid : 'draggable-child-card',
    data: {
      type: 'target',
      data: target,
    },
  });

  const child = target.children?.[0] ? target.children[0] : target;

  // Simplified logic: use character limits only when in catalog with tabs
  const getCharacterLimit = () => {
    if (!isOnCatalogWithTabs) {
      return undefined;
    }
    return isInsideTargetCard ? 25 : 30;
  };

  const style = {
    transform: CSS.Translate.toString(transform),
    opacity: dragging ? 0 : 1,
  };

  return (
    <div
      ref={!disableDrag ? setNodeRef : undefined}
      style={!disableDrag ? style : undefined}
      {...(!disableDrag ? listeners : {})}
      {...(!disableDrag ? attributes : {})}
      className={`
        ${!disableDrag ? 'cursor-grab' : ''}
        ${
          dragging
            ? '!w-80  shadow-2xl z-[1000] cursor-grabbing transition-all duration-500 scale-75'
            : 'w-full'
        }
      `}
    >
      <Card.Root round="md" className="py-2">
        {!isOnCatalogWithTabs && showActions && (
          <div className="flex w-full items-center p-[12px] justify-end z-10">
            <Button
              variant="tertiary"
              size="icon"
              disabled={disableChildActions}
              onClick={() => onEditTarget?.(child)}
            >
              <ArrowUpRightSquare />
            </Button>

            <Button
              variant="tertiary"
              size="icon"
              disabled={disableChildActions}
              onClick={() => onRemoveActionClick?.([child])}
            >
              <Trash />
            </Button>
          </div>
        )}

        <Card.Header className="flex flex-row gap-4 items-center pb-1 pt-0">
          <div className="flex-shrink-0">
            {child.deliverable?.type === DeliverableTypeEnum.KPI ||
            child.deliverable?.deliverableType?.code ===
              DeliverableTypeEnum.KPI ? (
              <IconWrapper variant="secondary" round="md" size={42}>
                <BarChartFill size={24} />
              </IconWrapper>
            ) : (
              <IconWrapper variant="primary" round="md" size={42}>
                <ListCheck size={24} />
              </IconWrapper>
            )}
          </div>
          <Card.Title
            className={cn('flex flex-col max-w-4xl flex-shrink min-w-0', {
              'max-w-xs': isDrawer,
            })}
          >
            {isOnCatalogWithTabs ? (
              <TruncatedTitle
                title={child.deliverable?.name || ''}
                maxLength={getCharacterLimit()}
              />
            ) : (
              <Typography
                variant="body-md-regular"
                color="dark"
                className="font-semibold whitespace-nowrap overflow-hidden text-ellipsis min-w-0"
              >
                {child.deliverable?.name}
              </Typography>
            )}
            <Container className="flex flex-row items-center gap-4 min-w-0 overflow-hidden">
              <Typography
                variant="body-sm-regular"
                color="dark"
                className="flex flex-row items-center gap-1 flex-shrink-0"
              >
                <Person />
                {child.deliverable?.usage || 0}
              </Typography>
              <Typography
                variant="body-sm-regular"
                color="dark"
                className="flex flex-row items-center gap-1 flex-shrink-0"
              >
                <Scale />
                {child.weight}%
              </Typography>
            </Container>
          </Card.Title>
        </Card.Header>
        <Card.Content className="flex flex-col gap-2">
          <Typography variant="metadata-sm-regular">
            {child.deliverable?.calculationMethod}
          </Typography>
          <Container className="flex flex-col">
            <Typography variant="metadata-xs-bold" color="dark">
              SCOPE:
            </Typography>
            <Typography variant="metadata-sm-regular">{child.scope}</Typography>
          </Container>
          <TargetFooter
            target={target}
            isOnCatalogWithTabs={isOnCatalogWithTabs}
            showBadgesInProposalTab={showBadgesInProposalTab}
            onAcceptTarget={onAcceptTarget}
            isTargetAccepted={isTargetAccepted}
            isTargetLoading={isTargetLoading}
          />
        </Card.Content>
      </Card.Root>
    </div>
  );
}
