import { Target } from '~/shared/types/Target';
import { TargetTypeEnum } from '~/shared/utils/enums/target-type';

interface UseTargetActionsProps {
  target: Target;
  isOnCatalogWithTabs?: boolean;
  showBadgesInProposalTab?: boolean;
  onAcceptTarget?: (targetUid: string) => void;
  isTargetAccepted?: (targetUid: string) => boolean;
  isTargetLoading?: (targetUid: string) => boolean;
}

export function useTargetActions({
  target,
  isOnCatalogWithTabs = false,
  showBadgesInProposalTab = false,
  onAcceptTarget,
  isTargetAccepted,
  isTargetLoading,
}: UseTargetActionsProps) {
  const hasProposal = target.targetTypes?.some(
    t => t.type === TargetTypeEnum.PROPOSAL,
  );
  const hasFeedback = target.targetTypes?.some(
    t => t.type === TargetTypeEnum.FEEDBACK,
  );
  const hasFinal = target.targetTypes?.some(
    t => t.type === TargetTypeEnum.FINAL,
  );

  // Get agreement status from the most recent target type
  const feedbackTargetType = target.targetTypes?.find(
    t => t.type === TargetTypeEnum.FEEDBACK,
  );
  const finalTargetType = target.targetTypes?.find(
    t => t.type === TargetTypeEnum.FINAL,
  );

  // Determine agreement status based on the latest target type with agreement info
  let agreementStatus: boolean | null = null;
  if (finalTargetType?.agree !== undefined) {
    agreementStatus = finalTargetType.agree;
  } else if (feedbackTargetType?.agree !== undefined) {
    agreementStatus = feedbackTargetType.agree;
  }

  const isOnlyProposal = hasProposal && !hasFeedback && !hasFinal;
  const isProposalFeedback = hasProposal && hasFeedback && !hasFinal;
  const isAllThreeTypes = hasProposal && hasFeedback && hasFinal;

  // Show agreement badges based on actual agreement status
  const shouldShowIAgree =
    agreementStatus === true && (isProposalFeedback || isAllThreeTypes);
  const shouldShowDontAgree =
    agreementStatus === false &&
    (isOnlyProposal || isProposalFeedback || isAllThreeTypes);

  const shouldShowBadges =
    isOnCatalogWithTabs &&
    target.uid &&
    agreementStatus !== null &&
    (shouldShowIAgree || shouldShowDontAgree);

  const shouldShowAcceptButton =
    isOnCatalogWithTabs && target.uid && onAcceptTarget && isTargetAccepted;

  const isAccepted = target.uid ? isTargetAccepted?.(target.uid) : false;
  const isLoading = target.uid ? isTargetLoading?.(target.uid) || false : false;

  const handleAcceptTarget = () => {
    if (target.uid && onAcceptTarget) {
      onAcceptTarget(target.uid);
    }
  };

  return {
    shouldShowIAgree,
    shouldShowDontAgree,
    shouldShowBadges,
    shouldShowAcceptButton,
    isAccepted,
    isLoading,
    handleAcceptTarget,
  };
}
