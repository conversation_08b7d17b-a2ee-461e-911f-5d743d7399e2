import { Target } from '~/shared/types/Target';
import { TargetTypeEnum } from '~/shared/utils/enums/target-type';

interface UseTargetActionsProps {
  target: Target;
  isOnCatalogWithTabs?: boolean;
  showBadgesInProposalTab?: boolean;
  onAcceptTarget?: (targetUid: string) => void;
  isTargetAccepted?: (targetUid: string) => boolean;
  isTargetLoading?: (targetUid: string) => boolean;
}

export function useTargetActions({
  target,
  isOnCatalogWithTabs = false,
  showBadgesInProposalTab = false,
  onAcceptTarget,
  isTargetAccepted,
  isTargetLoading,
}: UseTargetActionsProps) {
  const hasProposal = target.targetTypes?.some(
    t => t.type === TargetTypeEnum.PROPOSAL,
  );
  const hasFeedback = target.targetTypes?.some(
    t => t.type === TargetTypeEnum.FEEDBACK,
  );
  const hasFinal = target.targetTypes?.some(
    t => t.type === TargetTypeEnum.FINAL,
  );

  const isOnlyProposal = hasProposal && !hasFeedback && !hasFinal;
  const isProposalFeedback = hasProposal && hasFeedback && !hasFinal;
  const isAllThreeTypes = hasProposal && hasFeedback && hasFinal;

  const shouldShowIAgree = isProposalFeedback || isAllThreeTypes;
  const shouldShowDontAgree = isOnlyProposal;

  const shouldShowBadges =
    isOnCatalogWithTabs &&
    showBadgesInProposalTab &&
    target.uid &&
    (shouldShowIAgree || shouldShowDontAgree);

  const shouldShowAcceptButton =
    isOnCatalogWithTabs && target.uid && onAcceptTarget && isTargetAccepted;

  const isAccepted = target.uid ? isTargetAccepted?.(target.uid) : false;
  const isLoading = target.uid ? isTargetLoading?.(target.uid) || false : false;

  const handleAcceptTarget = () => {
    if (target.uid && onAcceptTarget) {
      onAcceptTarget(target.uid);
    }
  };

  return {
    shouldShowIAgree,
    shouldShowDontAgree,
    shouldShowBadges,
    shouldShowAcceptButton,
    isAccepted,
    isLoading,
    handleAcceptTarget,
  };
}
