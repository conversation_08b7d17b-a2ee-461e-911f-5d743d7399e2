import React from 'react';
import {
  BarC<PERSON>Fill,
  ChevronDown,
  EyeSlash,
  ListCheck,
} from 'react-bootstrap-icons';
import {
  Button,
  DropdownMenu,
  ToggleGroup,
  Typography,
} from '@ghq-abi/design-system-v2';
import { useTranslate } from '@tolgee/react';

interface BaseOptionsFilterProps {
  toggleValues: (string | null)[];
  onToggleChange: (value: string[]) => void;
  isFunction?: false;
}

interface OptionsFilterWithFunctionProps {
  toggleValues: (string | null)[];
  onToggleChange: (value: string[]) => void;
  isFunction: true;
  selectedFunctions: string[];
  functions: string[];
  onFunctionToggle: (functionName: string) => void;
}

type OptionsFilterProps =
  | BaseOptionsFilterProps
  | OptionsFilterWithFunctionProps;

export function OptionsFilter(props: OptionsFilterProps) {
  const { t } = useTranslate();
  const { toggleValues, onToggleChange } = props;

  return (
    <div className="flex items-center gap-4">
      <ToggleGroup.Root
        type="multiple"
        className="flex items-center gap-4"
        value={toggleValues.filter((v): v is string => v !== null)}
        onValueChange={(value: string | string[]) => {
          const arrayValue = Array.isArray(value) ? value : [value];
          onToggleChange(arrayValue);
        }}
      >
        <ToggleGroup.Item value="KPI" className="p-2 h-8 gap-2">
          <BarChartFill />
          <Typography variant="metadata-sm-bold">{t('common_kpi')}</Typography>
        </ToggleGroup.Item>

        <ToggleGroup.Item value="PROJECT" className="p-2 h-8 gap-2">
          <ListCheck />
          <Typography variant="metadata-sm-bold">
            {t('common_project')}
          </Typography>
        </ToggleGroup.Item>

        <div className="border-l-[1px] border-[#CACDD5] h-8" />

        <ToggleGroup.Item value="INACTIVE" className="p-2 h-8 gap-2">
          <EyeSlash />
          <Typography variant="metadata-sm-bold">
            {t('common_status_inactive')}
          </Typography>
        </ToggleGroup.Item>
      </ToggleGroup.Root>

      {props.isFunction && (
        <DropdownMenu.Root>
          <DropdownMenu.Trigger asChild>
            <Button
              variant={
                props.selectedFunctions.length > 0 ? 'light' : 'secondary'
              }
              border="default"
              iconRight={<ChevronDown />}
              className="p-2 h-8 gap-2"
            >
              <Typography variant="metadata-sm-bold">
                {t('common_functions')}
              </Typography>
            </Button>
          </DropdownMenu.Trigger>

          <DropdownMenu.Content className="w-56">
            {props.functions.map(functionName => (
              <DropdownMenu.CheckboxItem
                key={functionName}
                onClick={() => props.onFunctionToggle(functionName)}
                checked={props.selectedFunctions.includes(functionName)}
              >
                {functionName}
              </DropdownMenu.CheckboxItem>
            ))}
          </DropdownMenu.Content>
        </DropdownMenu.Root>
      )}
    </div>
  );
}
