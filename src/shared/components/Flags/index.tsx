import { useFlags } from '~/app/contexts/FlagsContext';
import { type PagesPathsKeys } from '~/shared/constants/pages';

type FlagsProps = {
  children: React.ReactNode;
  authorizedFlags: PagesPathsKeys | PagesPathsKeys[];
};

export function Flags({ children, authorizedFlags }: FlagsProps) {
  const flags = useFlags();

  if (typeof authorizedFlags === 'string' && !flags[authorizedFlags]?.active) {
    return null;
  }

  if (
    Array.isArray(authorizedFlags) &&
    !authorizedFlags.some(flag => flags[flag].active)
  ) {
    return null;
  }

  return <>{children}</>;
}
