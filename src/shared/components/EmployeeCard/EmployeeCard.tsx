import { Person, Trash } from 'react-bootstrap-icons';
import {
  <PERSON>ton,
  Card,
  Container,
  IconWrapper,
  Typography,
} from '@ghq-abi/design-system-v2';

import type { EmployeeCardProps } from './types';

export function EmployeeCard({
  name,
  department,
  onDelete,
}: EmployeeCardProps) {
  return (
    <Card.Root>
      <Card.Content className="flex justify-between items-center p-2">
        <Container className="flex items-center justify-start gap-4 w-full">
          <IconWrapper size={32} variant="primary" round="pill">
            <Person />
          </IconWrapper>
          <Container className="flex flex-col items-start">
            <Typography variant="body-md-bold">{name}</Typography>
            <Typography variant="body-sm-regular">{department}</Typography>
          </Container>
        </Container>
        <Container className="flex items-center w-full justify-end gap-2">
          {onDelete && (
            <Button variant="tertiary" onClick={onDelete}>
              <Trash />
            </Button>
          )}
        </Container>
      </Card.Content>
    </Card.Root>
  );
}
