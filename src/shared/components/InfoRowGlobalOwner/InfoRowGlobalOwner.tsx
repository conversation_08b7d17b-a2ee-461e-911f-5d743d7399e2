import { Globe } from 'react-bootstrap-icons';
import { Container, Skeleton, Typography } from '@ghq-abi/design-system-v2';

import { Avatar } from '../Avatar';

import { InfoRowGlobalOwnerProps } from './types';

export function InfoRowGlobalOwner({
  value,
  isLoading,
}: InfoRowGlobalOwnerProps) {
  return (
    <Container className="flex align-top gap-4">
      <Globe size={20} />
      <Container className="flex flex-col gap-2">
        <Typography variant="body-sm-bold">GLOBAL OWNERS</Typography>
        <ul className="flex flex-col gap-2">
          {value &&
            value.map((item, index) => (
              <li key={index} className="flex items-center gap-2">
                {isLoading ? (
                  <Skeleton className="h-5 w-32 rounded" />
                ) : (
                  <Container className="flex items-center gap-2">
                    <Avatar
                      name={item.name}
                      globalId={item.globalId}
                      size={24}
                    />
                    <Typography variant="metadata-sm-regular" color="light">
                      {item.name}
                    </Typography>
                  </Container>
                )}
              </li>
            ))}
        </ul>
      </Container>
    </Container>
  );
}
