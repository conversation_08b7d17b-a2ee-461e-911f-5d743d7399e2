import { Container, Skeleton, Typography } from '@ghq-abi/design-system-v2';

import { InfoRowProps } from './types';

export function InfoRow({ icon: Icon, label, value, isLoading }: InfoRowProps) {
  const renderValue = (item: string | undefined) => {
    switch (true) {
      case isLoading && !item:
        return <Skeleton className="h-5 w-32 rounded" />;
      case !isLoading && !item:
        return (
          <Typography variant="body-sm-regular" color="light">
            Not available
          </Typography>
        );
      default:
        return (
          <Typography variant="body-sm-regular" color="light">
            {item}
          </Typography>
        );
    }
  };

  if (Array.isArray(value)) {
    return (
      <Container className="flex flex-col gap-2">
        <Typography variant="body-sm-bold">{label}</Typography>
        <ul className="flex flex-col gap-2">
          {value.length > 0 ? (
            value.map((item, index) => (
              <li key={index} className="flex items-center gap-2">
                <Icon size={20} />
                {renderValue(item)}
              </li>
            ))
          ) : (
            <li className="flex items-center gap-2">
              <Icon size={20} />
              {renderValue(undefined)}
            </li>
          )}
        </ul>
      </Container>
    );
  }

  return (
    <Container className="flex items-center gap-4">
      <Icon size={20} />
      <Container className="flex flex-col">
        <Typography variant="body-sm-bold">{label}</Typography>
        {renderValue(value as string | undefined)}
      </Container>
    </Container>
  );
}
