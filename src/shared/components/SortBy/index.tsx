import React from 'react';
import { Filter } from 'react-bootstrap-icons';
import { Button, DropdownMenu, Typography } from '@ghq-abi/design-system-v2';
import { useTranslate } from '@tolgee/react';

import { OrderBy, SortOptions } from '~/shared/types/Sort';

type SortDropdownProps = {
  orderBy?: OrderBy | null | undefined;
  onSortOptionsChange: (options: SortOptions | null) => void;
};

export function SortDropdown({
  orderBy,
  onSortOptionsChange,
}: SortDropdownProps) {
  const { t } = useTranslate();

  const getCurrentValue = () => {
    if (orderBy) {
      return `${orderBy}`;
    }
    return '';
  };

  const handleValueChange = (value: string) => {
    if (!value.trim()) {
      onSortOptionsChange(null);
    } else {
      onSortOptionsChange({
        orderBy: value as OrderBy,
      });
    }
  };

  return (
    <DropdownMenu.Root>
      <DropdownMenu.Trigger asChild>
        <Button
          variant="tertiary"
          border="default"
          size="icon"
          className="flex-shrink-0"
        >
          <Filter className="w-6 h-6" />
        </Button>
      </DropdownMenu.Trigger>
      <DropdownMenu.Content className="w-48">
        <DropdownMenu.RadioGroup
          className="flex flex-col gap-2"
          value={getCurrentValue()}
          onValueChange={handleValueChange}
        >
          <DropdownMenu.RadioItem value={OrderBy.ASC} className="w-full">
            <Typography variant="body-sm-regular">{t('common_a_z')}</Typography>
          </DropdownMenu.RadioItem>
          <DropdownMenu.RadioItem value={OrderBy.DESC} className="w-full">
            <Typography variant="body-sm-regular">{t('common_z_a')}</Typography>
          </DropdownMenu.RadioItem>
        </DropdownMenu.RadioGroup>
      </DropdownMenu.Content>
    </DropdownMenu.Root>
  );
}
