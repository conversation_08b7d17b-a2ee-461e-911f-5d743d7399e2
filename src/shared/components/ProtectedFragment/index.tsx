import React from 'react';
import { useSession } from 'next-auth/react';

import { ProtectedFragmentProps } from './types';

export function ProtectedFragment({
  children,
  requiredRoles = [],
  fallback = null,
}: ProtectedFragmentProps) {
  const { data: session } = useSession();

  const userRoles = session?.user?.roles || [];

  const hasRequiredRoles = requiredRoles.every(role =>
    userRoles.includes(role),
  );

  if (hasRequiredRoles) {
    return <>{children}</>;
  }

  return <>{fallback}</>;
}
