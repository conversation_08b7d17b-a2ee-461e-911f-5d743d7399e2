import { CSS, styled, Table } from '@ghq-abi/design-system';

export const tableContainerStyles: CSS = {
  display: 'flex',
  flexDirection: 'column',
  flex: 1,
  maxHeight: '475px',
  overflowY: 'auto',
  '&::-webkit-scrollbar': {
    width: '10px',
  },
  '&::-webkit-scrollbar-thumb': {
    bg: '$gray575',
    border: '2px 3px 2px 3px',
    borderColor: 'transparent',
    borderStyle: 'solid',
    borderRadius: '$4',
    backgroundClip: 'content-box',
  },
  '&::-webkit-scrollbar-track': {
    bg: '#F3F3F3',
  },
};

const stickyStyles: CSS = {
  '@lg': {
    position: 'sticky',
    backgroundColor: '$white',
    left: 0,
    zIndex: 1,
  },
};

export const StyledColumn = styled(Table.Td, {
  py: '2-5',
  px: '$md',
});

export const StyledTableTh = styled(Table.Th, {
  borderTop: '0 !important',
  justifyContent: 'flex-start !important',
  minWidth: '$14',

  variants: {
    sticky: {
      true: stickyStyles,
    },
  },
});

export const StyledTableTd = styled(Table.Td, {
  border: 'none !important',
  whiteSpace: 'nowrap',
  minWidth: '$16',
  textTransform: 'capitalize',
  fontSize: '$xs',

  '@lg': {
    fontSize: '$sm',
  },

  variants: {
    sticky: {
      true: stickyStyles,
    },
  },
});

export const StyledTableRow = styled(Table.Tr, {
  transition: '$quickly-linear',
  cursor: 'pointer',
  position: 'relative',
  height: '66px',

  variants: {
    isBoldText: {
      true: {
        fontWeight: '$bold',
      },
    },
    disabled: {
      true: {
        pointerEvents: 'none',
      },
    },
  },

  '&:hover': {
    filter: 'brightness(0.96)',

    '[data-button-wrapper]': {
      display: 'flex',
    },

    '[data-spacing-td]': {
      display: 'none',
    },
  },
});
