import { ReactElement, ReactNode } from 'react';
import { CSS } from '@ghq-abi/design-system';

type ColumnType = 'check' | 'sort' | 'stick';

export type NestedKeyOf<ObjectType extends object> = {
  [Key in keyof ObjectType & (string | number)]: ObjectType[Key] extends object
    ? `${Key}` | `${Key}.${NestedKeyOf<ObjectType[Key]>}`
    : `${Key}`;
}[keyof ObjectType & (string | number)];

export interface Column<T extends { id: string }> {
  name: string;
  head?: ReactNode;
  key: NestedKeyOf<T>;
  render?: (data: Row<T>) => ReactNode;
  renderCustomColumn?: (data: Row<T>) => ReactNode;
  type?: ColumnType;

  tooltip?: ReactNode;
  css?: CSS;
  thCss?: CSS;
}

export interface TableActions<T extends { id: string }> {
  onSort?: (column: keyof T) => void;
  onCheck?: () => void;

  onRowClick?: (row: Row<T>) => void;
}

export type RowVariant =
  | 'active'
  | 'default'
  | 'error'
  | 'disabled'
  | 'checked';

export type RowState = {
  isChecked?: boolean;
};

export type Row<T extends { id: string }> = T & {
  state?: RowState;
  variant: RowVariant;
};

export interface TableProps<T extends { id: string }> extends TableActions<T> {
  columns: Column<T>[];
  tableState?: TableState<T>;
  isLoading?: boolean;
  withStickedHeader?: boolean;
  hasFetchError?: boolean;
  fetchError?: { message?: string; status?: boolean };
  containerCSS?: CSS;
  rowCSS?: CSS;
  headerCSS?: CSS;
  renderSkeleton?: ReactElement;
  allChecked?: boolean;
  disableCheck?: boolean;
  bordered?: boolean;
  responsive?: boolean;
  emptyTableMessage?: string;
}

export type TableState<T extends { id: string }> = {
  sortKey: keyof T | null;
  sortDirection: 'desc' | 'asc';
  rows: Row<T>[];
};
