import { useCallback, useEffect, useState } from 'react';

export function useTableSearch<T>(initialData: T[]) {
  const [filteredData, setFilteredData] = useState<T[]>(initialData);
  const [searchValue, setSearchValue] = useState<{
    value: string;
    filterProperties: Array<keyof T>;
    identicalSearch: boolean;
  }>({
    value: '',
    filterProperties: [],
    identicalSearch: false,
  });

  const handleSearch = useCallback(
    (
      value: string,
      filterProperties: Array<keyof T>,
      identicalSearch = false,
    ) => {
      setSearchValue({
        value,
        filterProperties,
        identicalSearch,
      });
    },
    [],
  );

  useEffect(() => {
    if (searchValue.value === '' && initialData.length) {
      setFilteredData(initialData);
      return;
    }

    const lowerCaseSearchValue = searchValue.value.toLowerCase();
    if (initialData) {
      const filtered = initialData.filter(row => {
        return searchValue.filterProperties.some(property => {
          const currentValue = String(row[property]).toLowerCase();

          if (searchValue.identicalSearch) {
            return lowerCaseSearchValue === currentValue;
          }

          const result = lowerCaseSearchValue
            .split(' ')
            .every(searchToken => currentValue.includes(searchToken));

          return result;
        });
      });

      setFilteredData(filtered);
    }
  }, [searchValue, initialData]);

  return {
    filteredData,
    handleSearch,
  };
}
