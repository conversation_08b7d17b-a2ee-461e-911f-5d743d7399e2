import { useCallback } from 'react';
import { Checkbox, CSS, Table as DSTable, Text } from '@ghq-abi/design-system';
import { useTranslate } from '@tolgee/react';

import { OrderButton } from '../OrderButton';

import { CustomColumn } from './CustomColumn';
import { Row, RowVariant, TableProps } from './types';

export function Table<T extends { id: string }>({
  columns,
  onSort,
  onCheck,
  onRowClick,
  tableState,
  isLoading,
  hasFetchError,
  containerCSS,
  fetchError,
  withStickedHeader,
  headerCSS,
  allChecked,
  disableCheck,
  renderSkeleton,
  emptyTableMessage,
  bordered = false,
  responsive = true,
}: TableProps<T>) {
  const { t } = useTranslate();

  const isReadyToLoad = !isLoading && !hasFetchError;

  const getRowBackground = useCallback((variant?: RowVariant) => {
    switch (variant) {
      case 'checked':
        return '$blue50';
      case 'active':
      case 'disabled':
        return '#F3F3F3';
      case 'error':
        return '#FBD5D5';

      default:
        return '$white';
    }
  }, []);

  const getTableRowCss = (row: Row<T>): CSS => {
    const defaultCss: CSS = {
      bg: getRowBackground(row.variant),
    };

    if (!onRowClick) {
      return defaultCss;
    }

    return {
      ...defaultCss,
      cursor: 'pointer',
      '&:hover': {
        bg: row.variant === 'disabled' ? 'inherit' : '$gray50',
      },
    };
  };

  return (
    <DSTable
      responsive={responsive}
      bordered={bordered}
      containerCss={{ ...containerCSS }}
    >
      <DSTable.Thead
        css={{
          position: withStickedHeader ? 'sticky' : 'relative',
          bg: '$gray100',
          top: 0,
          zIndex: '$docked',
          ...headerCSS,
        }}
      >
        <DSTable.Tr>
          {columns.map(col =>
            col.renderCustomColumn ? null : (
              <DSTable.Th
                key={col.name}
                css={{
                  px: '$md',
                  py: '$sm',
                  minWidth: col.type !== 'check' ? '250px' : 'unset',
                  ...col.thCss,
                }}
              >
                {!col.type && !col.head && col.name}

                {!col.type && col.head}

                {col.type === 'sort' && (
                  <OrderButton
                    direction={
                      tableState?.sortKey === col.key
                        ? tableState.sortDirection
                        : 'desc'
                    }
                    onClick={() => onSort?.(col.key as keyof T)}
                    rightAddon={col.tooltip ?? null}
                  >
                    {col.name}
                  </OrderButton>
                )}

                {col.type === 'check' && (
                  <Checkbox
                    size="md"
                    title={t('admin.my_scope.table.select_all', {
                      ns: 'admin',
                    })}
                    disabled={isLoading || disableCheck}
                    checked={
                      (!!tableState?.rows.length &&
                        tableState?.rows.every(r => r.state?.isChecked)) ||
                      allChecked
                    }
                    onCheckedChange={() => onCheck?.()}
                  />
                )}
              </DSTable.Th>
            ),
          )}
          <DSTable.Th
            css={{ border: '0 !important', '@lg': { display: 'none' } }}
          />
        </DSTable.Tr>
      </DSTable.Thead>
      <DSTable.Tbody
        css={{
          flex: 1,
        }}
      >
        {fetchError?.status && (
          <DSTable.Tr>
            <DSTable.Td
              colSpan={8}
              css={{
                border: 0,
                px: '$md',
                py: '2-5',
              }}
            >
              <Text css={{ textAlign: 'center' }}>
                {fetchError?.message ??
                  t('common.error_to_fetch', { t: '', ns: 'default' })}
              </Text>
            </DSTable.Td>
          </DSTable.Tr>
        )}

        {isReadyToLoad && tableState?.rows.length === 0 && (
          <DSTable.Tr>
            <DSTable.Td
              colSpan={8}
              css={{
                px: '$md',
                py: '2-5',
              }}
            >
              <Text css={{ textAlign: 'center' }}>
                {emptyTableMessage ??
                  t('common.no_results_found', { ns: 'default' })}
              </Text>
            </DSTable.Td>
          </DSTable.Tr>
        )}

        {isLoading && renderSkeleton && renderSkeleton}

        {isReadyToLoad &&
          tableState?.rows?.map(row => (
            <DSTable.Tr
              key={row.id}
              onClick={() => onRowClick?.(row)}
              css={getTableRowCss(row)}
            >
              {columns.map(col => (
                <CustomColumn
                  key={`${col.name}-${row[col.key as keyof T]}`}
                  column={{
                    ...col,
                    css: {
                      border: bordered ? 'unset' : '0 !important',
                      ...col.css,
                    },
                  }}
                  row={row}
                />
              ))}
            </DSTable.Tr>
          ))}
      </DSTable.Tbody>
    </DSTable>
  );
}
