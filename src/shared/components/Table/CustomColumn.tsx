/* eslint-disable react/destructuring-assignment */
import { StyledColumn } from './styles';
import { Column, Row } from './types';

type CustomColumnProps<T extends { id: string }> = {
  column: Column<T>;
  row: Row<T>;
};
export function CustomColumn<T extends { id: string }>({
  column,
  row,
}: CustomColumnProps<T>) {
  if (column?.renderCustomColumn) {
    return <>{column.renderCustomColumn(row)}</>;
  }

  return (
    <StyledColumn css={column.css}>
      {column.render
        ? column.render(row)
        : (row[column.key as keyof T] as string) ?? '-'}
    </StyledColumn>
  );
}
