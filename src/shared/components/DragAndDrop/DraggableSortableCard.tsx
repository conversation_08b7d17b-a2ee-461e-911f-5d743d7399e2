import React from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

export interface SortableDeliverableCardProps {
  id: string;
  children: React.ReactNode;
}

export function DraggableSortableCard({
  id,
  children,
}: SortableDeliverableCardProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: id,
    transition: {
      duration: 300,
      easing: 'ease-out',
    },
  });

  const style = {
    transform: isDragging
      ? `${CSS.Transform.toString(transform)} scale(0.97)`
      : CSS.Transform.toString(transform),
    transition: transition || 'transform 200ms ease-out',
    opacity: isDragging ? 0.8 : 1,
    zIndex: isDragging ? 1000 : 1,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...listeners}
      className={`${isDragging ? 'cursor-grabbing' : 'cursor-grab'} touch-none`}
    >
      {children}
    </div>
  );
}
