import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from 'react-bootstrap-icons';
import {
  Card,
  Container,
  IconWrapper,
  Typography,
} from '@ghq-abi/design-system-v2';

import { DeliverableTypeEnum } from '~/shared/types/DeliverableType';

type SimpleDeliverableCardProps = {
  name: string;
  businessFunction: string;
  usage: number;
  deliverableType?: DeliverableTypeEnum;
};

export function SimpleDeliverableCard({
  name,
  businessFunction,
  usage,
  deliverableType = DeliverableTypeEnum.KPI,
}: SimpleDeliverableCardProps) {
  return (
    <Card.Root round="md">
      <Card.Content className="flex flex-row gap-4 items-center">
        {deliverableType === DeliverableTypeEnum.KPI ? (
          <IconWrapper variant="secondary" round="md" size={42}>
            <BarChartFill size={24} />
          </IconWrapper>
        ) : (
          <IconWrapper variant="primary" round="md" size={42}>
            <ListCheck size={24} />
          </IconWrapper>
        )}
        <Container>
          <Typography
            variant="body-md-regular"
            color="dark"
            className="font-semibold"
          >
            {name}
          </Typography>
          <Container className="flex flex-row gap-4">
            <Typography
              variant="body-sm-regular"
              color="dark"
              className="flex flex-row items-center gap-1"
            >
              {businessFunction}
            </Typography>
            <Container className="flex flex-row items-center gap-4">
              <Typography
                variant="body-sm-regular"
                color="dark"
                className="flex flex-row items-center gap-1"
              >
                <Person />
                {usage}
              </Typography>
            </Container>
          </Container>
        </Container>
      </Card.Content>
    </Card.Root>
  );
}
