import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  EyeSlash,
  <PERSON>Che<PERSON>,
} from 'react-bootstrap-icons';
import {
  <PERSON><PERSON>,
  Card,
  Container,
  IconWrapper,
  Typography,
} from '@ghq-abi/design-system-v2';

import { DeliverableTypeEnum } from '~/shared/types/Deliverable';
import { Owners } from '~/shared/types/Owners';
import { cn } from '~/shared/utils/cn';
import { cleanInvisibleCharacters } from '~/shared/utils/string';

import { Avatar } from '../Avatar';

import type { CatalogItemDetailCardProps } from './types';

export function CatalogItemDetailCardSimple({
  info,
  content,
  onCopy,
  icon = null,
  className,
  isActive,
  type,
}: CatalogItemDetailCardProps) {
  const isType = cleanInvisibleCharacters(info);
  const iconVariant =
    type !== DeliverableTypeEnum.KPI ? 'primary' : 'secondary';

  return (
    <Card.Root key={info} className={cn('p-4', className)}>
      <Card.Header className="p-0 max-h-8">
        <Card.Title className="flex items-center gap-4">
          {isType === 'Type' && !isActive ? (
            <div className="relative">
              <IconWrapper variant={iconVariant} round="md" size={32}>
                {icon}
              </IconWrapper>
              <IconWrapper variant="disabled">
                <EyeSlash
                  className="absolute bottom-0 right-0 left-4 bg-white rounded-full p-0.5"
                  style={{ transform: 'translate(30%, 30%)' }}
                  size={16}
                />
              </IconWrapper>
            </div>
          ) : (
            <IconWrapper variant={iconVariant} round="md" size={32}>
              {icon && icon}
            </IconWrapper>
          )}
          <div className="flex flex-col">
            <Typography variant="metadata-sm-bold" className="uppercase">
              {info}
            </Typography>
            <Typography variant="body-sm-regular" color="light">
              {typeof content === 'string' || typeof content === 'number'
                ? content
                : 'No information available'}
            </Typography>
          </div>
          <Button
            variant="tertiary"
            onClick={onCopy}
            className="ml-auto w-8 h-8"
          >
            <Copy />
          </Button>
        </Card.Title>
      </Card.Header>
    </Card.Root>
  );
}

export function CatalogItemDetailCardText({
  info,
  onCopy,
  content,
  icon = null,
  className,
  type,
}: CatalogItemDetailCardProps) {
  const iconVariant =
    type !== DeliverableTypeEnum.KPI ? 'primary' : 'secondary';

  return (
    <Card.Root className={cn('p-4 flex flex-col', className)}>
      <Card.Header className="p-0 max-h-8 flex-shrink-0">
        <Card.Title className="flex items-center gap-4">
          <IconWrapper variant={iconVariant} round="md" size={32}>
            {icon && icon}
          </IconWrapper>
          <div className="flex flex-col">
            <Typography variant="metadata-sm-bold" className="uppercase">
              {info}
            </Typography>
          </div>
          <Button
            variant="tertiary"
            onClick={onCopy}
            className="ml-auto w-8 h-8"
          >
            <Copy />
          </Button>
        </Card.Title>
      </Card.Header>
      <Card.Content className="mt-4 p-0 flex-1 min-h-0">
        <Container
          background="light"
          border="dark"
          round="md"
          className="p-2 h-full"
        >
          <Typography variant="body-sm-regular" color="light">
            {typeof content === 'string' ? content : 'No information available'}
          </Typography>
        </Container>
      </Card.Content>
    </Card.Root>
  );
}

export function CatalogItemDetailCardBulletList({
  info,
  content,
  onCopy,
  icon = null,
  className,
  type,
}: CatalogItemDetailCardProps) {
  const iconVariant =
    type !== DeliverableTypeEnum.KPI ? 'primary' : 'secondary';

  return (
    <Card.Root className={cn('p-4', className)}>
      <Card.Header className="p-0 max-h-8">
        <Card.Title className="flex items-center gap-2">
          <IconWrapper variant={iconVariant} round="md" size={32}>
            {icon && icon}
          </IconWrapper>
          <Typography variant="metadata-sm-bold" className="uppercase">
            {info}
          </Typography>
          <Button
            variant="tertiary"
            onClick={onCopy}
            className="ml-auto w-8 h-8"
          >
            <Copy />
          </Button>
        </Card.Title>
      </Card.Header>
      <Card.Content className="px-0">
        <Container background="light" border="dark" round="md" className="p-2">
          <ul className="pl-5">
            {content &&
              Array.isArray(content) &&
              content.map((item, index) => (
                <li key={index} className="flex items-center gap-2">
                  <Dot size={24} />
                  <Typography variant="body-sm-regular" color="light">
                    {typeof item === 'string'
                      ? item
                      : 'No information available'}
                  </Typography>
                </li>
              ))}
          </ul>
        </Container>
      </Card.Content>
    </Card.Root>
  );
}

export function CatalogItemDetailCardGlobalOwners({
  info,
  content,
  onCopy,
  icon = null,
  className,
  type,
}: CatalogItemDetailCardProps) {
  const iconVariant =
    type !== DeliverableTypeEnum.KPI ? 'primary' : 'secondary';

  return (
    <Card.Root className={cn('p-4', className)}>
      <Card.Header className="p-0 max-h-8">
        <Card.Title className="flex items-center gap-2">
          <IconWrapper variant={iconVariant} round="md" size={32}>
            {icon && icon}
          </IconWrapper>
          <Typography variant="metadata-sm-bold" className="uppercase">
            {info}
          </Typography>
        </Card.Title>
      </Card.Header>
      <Card.Content className="flex flex-col px-0 gap-4 overflow-y-auto h-[calc(100%-30px)]">
        {content &&
          Array.isArray(content) &&
          content.length > 0 &&
          (content as Owners[]).every(
            item => typeof item === 'object' && 'name' in item,
          ) &&
          (content as Owners[]).map(user => (
            <Container
              background="transparent"
              border="none"
              round="md"
              className="flex gap-2 items-center"
              key={user.globalId}
            >
              <Container
                background="transparent"
                border="none"
                round="md"
                className="flex gap-2 items-center"
              >
                <Avatar
                  globalId={String(user.globalId)}
                  name={user.name}
                  size={32}
                />
                {user.name && (
                  <Typography variant="body-sm-regular" color="light">
                    {user.name}
                  </Typography>
                )}
              </Container>
              <Button
                variant="tertiary"
                onClick={onCopy}
                className="ml-auto w-8 h-8"
              >
                <Copy />
              </Button>
            </Container>
          ))}
      </Card.Content>
    </Card.Root>
  );
}

export function CatalogItemDetailCardDeliverables({
  info,
  content,
  icon = null,
  className,
  type,
}: CatalogItemDetailCardProps) {
  const iconVariant =
    type !== DeliverableTypeEnum.KPI ? 'primary' : 'secondary';

  return (
    <Card.Root className={cn('p-4', className)}>
      <Card.Header className="p-0 max-h-8">
        <Card.Title className="flex items-center gap-2">
          <IconWrapper variant={iconVariant} round="md" size={32}>
            {icon && icon}
          </IconWrapper>
          <Typography variant="metadata-sm-bold" className="uppercase">
            {info}
          </Typography>
        </Card.Title>
      </Card.Header>
      <Card.Content className="flex flex-col px-0 gap-4 h-[calc(100%-30px)] overflow-y-auto">
        {content &&
          Array.isArray(content) &&
          content.length > 0 &&
          (content as any[]).every(
            item => typeof item === 'object' && 'name' in item,
          ) &&
          (content as any[]).map((deliverable, index) => (
            <Container
              background="transparent"
              border="default"
              round="minimal"
              className="flex gap-2 items-center p-4 h-16 shadow-sm"
              key={index}
            >
              <IconWrapper
                variant={
                  deliverable.type === DeliverableTypeEnum.KPI
                    ? 'secondary'
                    : 'primary'
                }
                round="md"
                size={32}
              >
                {deliverable.type === DeliverableTypeEnum.KPI ? (
                  <BarChartLineFill />
                ) : (
                  <ListCheck />
                )}
              </IconWrapper>
              <Typography variant="body-sm-bold">{deliverable.name}</Typography>
            </Container>
          ))}
      </Card.Content>
    </Card.Root>
  );
}
