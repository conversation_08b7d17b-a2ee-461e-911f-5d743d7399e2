import { Check, Exclamation } from 'react-bootstrap-icons';
import {
  <PERSON><PERSON>,
  Card,
  Container,
  Dialog,
  IconWrapper,
  Typography,
} from '@ghq-abi/design-system-v2';
import { useTranslate } from '@tolgee/react';

export function ActionModal({
  isOpen,
  openModal,
  closeModal,
  title,
  variant,
  message,
  actions,
}: ActionModalProps) {
  const { t } = useTranslate();

  return (
    <Dialog.Root
      onOpenChange={open => (open ? openModal() : closeModal())}
      open={isOpen}
    >
      <Dialog.Trigger asChild />
      <Dialog.Content className="rounded-lg max-w-lg">
        <Dialog.Header className="flex flex-col items-start justify-center">
          <Dialog.Title>
            <Typography variant="title-md-regular" className="font-semibold">
              {title}
            </Typography>
          </Dialog.Title>
        </Dialog.Header>
        <Dialog.Description className="p-2">
          <Card.Root className="bg-gray-50 border-gray-100">
            <Card.Content className="flex flex-col items-center gap-4 p-4">
              {variant === 'success' ? (
                <IconWrapper round="full" variant="success" size={96}>
                  <Check size={64} />
                </IconWrapper>
              ) : (
                <IconWrapper round="full" variant="primary" size={96}>
                  <Exclamation size={64} />
                </IconWrapper>
              )}
              <Typography
                variant="body-sm-medium"
                color="light"
                className="text-center font-semibold"
              >
                {message}
              </Typography>
            </Card.Content>
          </Card.Root>
        </Dialog.Description>
        <Dialog.Footer className="flex gap-2 justify-end">
          {actions.length > 0 ? (
            actions.map((action, index) => (
              <Button
                key={index}
                variant={action.variant || 'primary'}
                onClick={action.onClick}
                isLoading={action.isLoading}
                border={action.border || 'none'}
              >
                {action.label}
              </Button>
            ))
          ) : (
            <Dialog.Close asChild>
              <Button variant="primary" onClick={closeModal}>
                {t('common_understood')}
              </Button>
            </Dialog.Close>
          )}
        </Dialog.Footer>
      </Dialog.Content>
    </Dialog.Root>
  );
}
