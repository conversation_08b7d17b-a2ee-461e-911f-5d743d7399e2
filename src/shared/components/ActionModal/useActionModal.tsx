import { useState } from 'react';

type ActionModalAction = {
  label: string;
  onClick: () => void;
  isLoading?: boolean;
  variant?: 'primary' | 'secondary' | 'tertiary';
  border?: 'default' | 'none';
};

type OpenModalOptions = {
  title?: string;
  variant?: 'success' | 'warning';
  message?: string;
  actions?: ActionModalAction[];
  onConfirm?: () => void;
  onUnderstood?: () => void;
};
export function useActionModal() {
  const [isOpen, setIsOpen] = useState(false);
  const [title, setTitle] = useState('Action Required');
  const [variant, setVariant] = useState<'success' | 'warning'>('success');
  const [message, setMessage] = useState<string>('');
  const [actions, setActions] = useState<ActionModalAction[]>([]);

  const [onConfirmAction, setOnConfirmAction] = useState<(() => void) | null>(
    null,
  );
  const [onUnderstoodAction, setOnUnderstoodAction] = useState<
    (() => void) | null
  >(null);

  const openModal = (options?: {
    title?: string;
    variant?: 'success' | 'warning';
    message?: string;
    actions?: ActionModalAction[];
    onConfirm?: () => void;
    onUnderstood?: () => void;
  }) => {
    if (options?.title !== undefined) {
      setTitle(options.title);
    }
    if (options?.variant !== undefined) {
      setVariant(options.variant);
    }
    if (options?.message !== undefined) {
      setMessage(options.message);
    }
    if (options?.actions !== undefined) {
      setActions(options.actions);
    } else {
      setActions([]);
    }
    if (options?.onConfirm !== undefined) {
      setOnConfirmAction(() => options.onConfirm);
    } else {
      setOnConfirmAction(null);
    }
    if (options?.onUnderstood !== undefined) {
      setOnUnderstoodAction(() => options.onUnderstood);
    } else {
      setOnUnderstoodAction(null);
    }
    setIsOpen(true);
  };

  const closeModal = () => {
    setIsOpen(false);
    setOnConfirmAction(null);
    setOnUnderstoodAction(null);
  };

  const handleConfirm = () => {
    if (onConfirmAction) {
      onConfirmAction();
    }
  };

  const handleUnderstood = () => {
    if (onUnderstoodAction) {
      onUnderstoodAction();
    } else {
      // Default behavior: just close modal
      closeModal();
    }
  };

  return {
    title,
    variant,
    message,
    isOpen,
    actions,
    openModal,
    closeModal,
    onConfirm: onConfirmAction,
    handleConfirm,
    handleUnderstood,
  };
}
