import { BsExclamationTriangle } from 'react-icons/bs';
import { styled } from '@ghq-abi/design-system';

const StyledBannerContainer = styled('div', {
  display: 'none',
  bg: 'red',
  color: '$white',
  width: 'fit-content',
  borderRadius: '$4',
  whiteSpace: 'nowrap',
  alignItems: 'center',
  px: '$lg',
  py: '$sm',
  gap: '$sm',
  '@tabletSm': {
    display: 'flex',
  },
});

const StyledBannerText = styled('p', {
  color: '$white',
  fontWeight: '$bold',
  fontSize: '$2xl',
});

type EnvironmentBannerProps = {
  environment: string;
};
export function EnvironmentBanner({ environment }: EnvironmentBannerProps) {
  return (
    <StyledBannerContainer>
      <StyledBannerText>
        {environment.toUpperCase()} ENVIRONMENT
      </StyledBannerText>
    </StyledBannerContainer>
  );
}
