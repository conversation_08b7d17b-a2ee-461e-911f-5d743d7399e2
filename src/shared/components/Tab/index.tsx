import { Skeleton, Typography } from '@ghq-abi/design-system-v2';

import { Container } from '../CommentsModal/styles';

interface TabProps {
  step: number;
  changeStep: (step: number) => void;
  tabs: {
    title: string;
    stepNumber: number;
  }[];
  children: React.ReactNode;
  isLoading?: boolean;
}

export const Tab = ({
  step,
  changeStep,
  tabs,
  children,
  isLoading,
}: TabProps) => {
  return (
    <>
      {isLoading ? (
        <div className="flex flex-row gap-4">
          {tabs.map(tab => (
            <Skeleton key={tab.stepNumber} className="w-[80px] h-[20px]" />
          ))}
        </div>
      ) : (
        <Container className="flex flex-row gap-4 border-0 border-b border-gray-200">
          {tabs.map(tab => (
            <Container
              key={tab.stepNumber}
              className={`p-2 max-w-[80px] text-center cursor-pointer border-0 ${
                step === tab.stepNumber ? 'border-b-4 border-yellow-400' : ''
              }`}
              onClick={() => changeStep(tab.stepNumber)}
            >
              <Typography variant="body-sm-bold">{tab.title}</Typography>
            </Container>
          ))}
        </Container>
      )}
      <Container className="flex flex-col gap-4 overflow-y-auto h-[calc(100vh-220px)] overflow-x-hidden pb-4">
        {children}
      </Container>
    </>
  );
};
