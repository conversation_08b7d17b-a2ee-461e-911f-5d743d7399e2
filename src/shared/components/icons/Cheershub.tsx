type CheershubProps = {
  size?: number;
  color?: string;
};

export function Cheershub({
  size = 28,
  color = 'currentColor',
}: CheershubProps) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={size}
      height={size}
      viewBox="0 0 24 20"
      fill="none"
    >
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M22.9718 9.20533C22.9718 9.20533 22.5602 8.18513 21.7116 6.86289C20.1494 4.42846 17.1063 0.970163 12.425 0.970154C8.98143 0.970147 6.66458 1.47963 4.04535 3.71521C1.35844 6.00856 -2.13272e-06 8.27335 0 11.806C2.70401e-06 16.2848 1.5827 18.001 4.76773 18.5964C7.30201 19.0701 9.53547 18.163 10.8358 15.9958C11.7665 14.4446 11.7852 12.9254 11.8016 11.5895C11.8069 11.1594 11.8119 10.7482 11.8471 10.3612C11.9916 8.77193 13.3098 6.96417 16.9038 6.60475C18.2916 6.46596 19.7461 7.06038 20.883 7.71573C22.1133 8.42487 22.9718 9.20533 22.9718 9.20533ZM12.425 2.12596C15.5481 2.12597 17.8874 3.89484 19.5107 5.81735C18.6628 5.53116 17.7309 5.36045 16.7888 5.45467C14.8189 5.65167 13.3437 6.25909 12.3218 7.151C11.2926 8.04938 10.7942 9.17698 10.696 10.2565C10.6566 10.6898 10.6511 11.1415 10.6461 11.5574L10.6454 11.6094C10.6399 12.0553 10.6334 12.4782 10.597 12.9077C10.5255 13.7494 10.3411 14.5737 9.84466 15.4011C8.81478 17.1176 7.08263 17.8532 4.98011 17.4602C3.5458 17.1921 2.67146 16.7105 2.11568 15.966C1.53764 15.1917 1.15582 13.9373 1.15581 11.806C1.15581 10.198 1.46245 8.9369 2.03977 7.83057C2.6233 6.71233 3.51867 5.68433 4.79571 4.59434C6.00227 3.56451 7.09849 2.96426 8.26249 2.6101C9.44249 2.25107 10.7529 2.12596 12.425 2.12596ZM15.1539 9.97501C14.7484 9.69667 14.3484 9.47535 14.0188 9.31135C14.1336 9.22035 14.2662 9.12407 14.4166 9.02772C15.0777 8.60437 16.0881 8.17669 17.4745 8.19395C20.329 8.22948 22.6393 9.80754 22.8288 12.4257C22.9272 13.7851 22.5486 15.168 21.8196 16.1985C21.0962 17.2212 20.0497 17.8741 18.782 17.8741C17.255 17.8741 16.2397 17.6313 15.6227 17.404C15.6034 17.3969 15.5845 17.3898 15.566 17.3828C15.7367 17.2403 15.9194 17.0692 16.1004 16.8675C16.7831 16.1066 17.436 14.9133 17.3362 13.2169C17.2474 11.7076 16.1085 10.6301 15.1539 9.97501ZM14.0677 17.8761C14.0674 17.8758 14.0671 17.8755 14.4477 17.4406L14.0677 17.8761L13.3998 17.2916L14.201 16.918L14.202 16.9175C14.2038 16.9167 14.2078 16.9147 14.2138 16.9116C14.2259 16.9055 14.2462 16.8949 14.2735 16.8797C14.328 16.8494 14.41 16.801 14.5099 16.7334C14.7104 16.5976 14.978 16.3877 15.2402 16.0955C15.7578 15.5186 16.2608 14.617 16.1824 13.2847C16.1267 12.3378 15.3875 11.5372 14.4999 10.928C14.0723 10.6345 13.6453 10.4101 13.3236 10.2589C13.1635 10.1836 13.0312 10.1272 12.9404 10.0902C12.895 10.0717 12.86 10.0581 12.8372 10.0494C12.8258 10.045 12.8175 10.0419 12.8124 10.04L12.8072 10.0381L12.0691 9.7724L12.5416 9.14625L13.0029 9.49435C12.5416 9.14625 12.5414 9.14649 12.5416 9.14625L12.5424 9.14524L12.5433 9.14406L12.5455 9.14116L12.5516 9.13331C12.5563 9.12718 12.5626 9.11925 12.5704 9.10966C12.5859 9.09049 12.6073 9.06462 12.6347 9.03305C12.6896 8.96994 12.7685 8.8838 12.872 8.78254C13.0787 8.58025 13.3848 8.316 13.7933 8.0544C14.6134 7.52918 15.8424 7.01772 17.4889 7.03822C20.7002 7.0782 23.7332 8.90975 23.9816 12.3422C24.0983 13.9541 23.6549 15.6053 22.7633 16.8659C21.8661 18.1344 20.4934 19.0299 18.782 19.0299C17.1305 19.0299 15.9787 18.7669 15.2231 18.4886C14.8458 18.3496 14.5694 18.2075 14.3806 18.0942C14.2863 18.0376 14.2141 17.9884 14.1624 17.9506C14.1365 17.9316 14.1158 17.9156 14.1 17.9029C14.0921 17.8966 14.0854 17.8911 14.08 17.8865L14.0726 17.8803L14.0697 17.8778L14.0677 17.8761Z"
        fill={color}
      />
    </svg>
  );
}
