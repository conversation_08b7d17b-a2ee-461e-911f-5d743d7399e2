export function Scale() {
  return (
    <svg
      width="14"
      height="14"
      viewBox="0 0 14 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M13.9726 7.37597L11.9363 2.1632C11.9027 2.07722 11.8403 2.00625 11.7604 1.96293C11.6804 1.91962 11.5881 1.90677 11.4997 1.92668L7.38182 2.86367V1.26596C7.38182 1.16227 7.34159 1.06283 7.26999 0.989509C7.19838 0.91619 7.10126 0.875 7 0.875C6.89874 0.875 6.80162 0.91619 6.73001 0.989509C6.65841 1.06283 6.61818 1.16227 6.61818 1.26596V3.03765L2.33545 4.01504C2.27478 4.02884 2.21832 4.0576 2.17096 4.09882C2.12361 4.14004 2.0868 4.19248 2.06373 4.25157L0.0273637 9.46434C0.00982614 9.50957 0.000551061 9.55772 0 9.60638C0 11.0308 1.48336 11.5612 2.41818 11.5612C3.353 11.5612 4.83636 11.0308 4.83636 9.60638C4.83621 9.55662 4.82692 9.50733 4.809 9.46108L2.93936 4.67576L6.61818 3.83846V12.3431H5.47273C5.37146 12.3431 5.27435 12.3843 5.20274 12.4576C5.13114 12.5309 5.09091 12.6304 5.09091 12.734C5.09091 12.8377 5.13114 12.9372 5.20274 13.0105C5.27435 13.0838 5.37146 13.125 5.47273 13.125H8.52727C8.62854 13.125 8.72565 13.0838 8.79726 13.0105C8.86886 12.9372 8.90909 12.8377 8.90909 12.734C8.90909 12.6304 8.86886 12.5309 8.79726 12.4576C8.72565 12.3843 8.62854 12.3431 8.52727 12.3431H7.38182V3.66448L10.9588 2.85064L9.191 7.37597C9.17308 7.42223 9.16379 7.47151 9.16364 7.52128C9.16364 8.94567 10.647 9.47606 11.5818 9.47606C12.5166 9.47606 14 8.94567 14 7.52128C13.9998 7.47151 13.9906 7.42223 13.9726 7.37597ZM2.41818 10.7793C2.02907 10.7761 1.64683 10.674 1.30582 10.4821C0.964091 10.2782 0.787182 10.0143 0.765545 9.6761L2.42009 5.4466L4.07464 9.6761C4.01291 10.574 2.83691 10.7793 2.41818 10.7793ZM11.5818 8.69415C11.1927 8.69103 10.8105 8.58893 10.4695 8.39702C10.1277 8.19307 9.95082 7.92918 9.92918 7.591L11.5837 3.36149L13.2383 7.591C13.1765 8.4889 12.0005 8.69415 11.5818 8.69415Z"
        fill="#3F465A"
      />
    </svg>
  );
}
