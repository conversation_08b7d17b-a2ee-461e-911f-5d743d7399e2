import * as React from 'react';

type MoneyBagProps = {
  size?: number;
  color?: string;
};

export function MoneyBag({ size = 24, color = '#ffffff' }: MoneyBagProps) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={size}
      height={size}
      viewBox="0 0 16 16"
      fill="none"
    >
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M8.01747 1C6.16724 1 4.47658 1.55227 3.44606 2.01936V2.01975C3.3436 2.06603 3.24785 2.11154 3.15882 2.15587C2.97909 2.24455 2.82834 2.32817 2.70949 2.40012L4.25065 4.50029C0.643415 8.01536 -2.04459 15 8.01747 15C18.0795 15 15.3172 7.88624 11.741 4.50029L13.2923 2.40012C13.1167 2.29161 12.8673 2.15782 12.5582 2.01625L12.5003 1.99019C11.4752 1.52932 9.82907 1 8.01747 1ZM11.9859 2.79098L10.7441 4.47229C9.01565 5.04128 6.97225 5.04128 5.24422 4.47151L4.43794 3.37203C4.6791 3.43002 4.92239 3.48009 5.16737 3.52215C6.13952 3.68822 7.18936 3.72673 8.13421 3.47393C9.45701 3.11923 10.831 2.86799 11.9859 2.79098ZM11.2333 5.13346C9.21134 5.85763 6.78118 5.85763 4.7592 5.13423C3.94998 5.94202 3.19704 6.93338 2.65742 7.96325C2.09177 9.04328 1.78942 10.1101 1.84611 11.0241C1.90028 11.9038 2.28284 12.6552 3.1542 13.2145C4.0642 13.7978 5.58773 14.2222 8.01705 14.2222C10.4439 14.2222 11.9602 13.7905 12.8618 13.2005C13.7269 12.6342 14.1057 11.8723 14.1544 10.9805C14.2048 10.0564 13.8957 8.98417 13.328 7.91152C12.7834 6.89386 12.0773 5.95746 11.2333 5.13346ZM5.31939 2.75715C4.96328 2.69609 4.61222 2.61558 4.27627 2.52302C5.22112 2.1516 6.57037 1.77784 8.01705 1.77784C8.9661 1.77784 9.8698 1.93886 10.6425 2.15432C9.71655 2.29241 8.80074 2.48348 7.90031 2.72642C7.12763 2.93372 6.22225 2.91116 5.31939 2.75715ZM9.39094 7.50805C9.67461 7.69381 9.88913 7.95644 10.005 8.25979C10.0233 8.30796 10.0313 8.359 10.0283 8.41C10.0254 8.46101 10.0117 8.51098 9.98786 8.55706C9.96408 8.60314 9.93072 8.64443 9.88971 8.67858C9.84869 8.71272 9.80081 8.73905 9.74881 8.75606C9.69681 8.77306 9.6417 8.78042 9.58662 8.7777C9.53155 8.77498 9.4776 8.76224 9.42784 8.74021C9.32735 8.69572 9.25006 8.61608 9.21297 8.51881C9.15505 8.36716 9.04778 8.23587 8.90594 8.14302C8.7641 8.05018 8.59467 8.00034 8.42098 8.00038V9.55607C8.86647 9.55607 9.29372 9.71997 9.60873 10.0117C9.92374 10.3035 10.1007 10.6992 10.1007 11.1118C10.1007 11.5243 9.92374 11.92 9.60873 12.2118C9.29372 12.5035 8.86647 12.6674 8.42098 12.6674V13.0564H7.58111V12.6674C7.23369 12.6674 6.89481 12.5677 6.61114 12.3819C6.32747 12.1962 6.11295 11.9335 5.99711 11.6302C5.97691 11.5817 5.96742 11.5299 5.96921 11.4779C5.97099 11.4259 5.98402 11.3748 6.0075 11.3276C6.03099 11.2803 6.06447 11.238 6.10596 11.2029C6.14744 11.1679 6.1961 11.1409 6.24904 11.1236C6.30198 11.1062 6.35814 11.0989 6.41418 11.1021C6.47023 11.1052 6.52503 11.1187 6.57535 11.1418C6.62566 11.1649 6.67046 11.197 6.70711 11.2365C6.74375 11.2759 6.77149 11.3217 6.78869 11.3712C6.90459 11.6737 7.21576 11.8896 7.58111 11.8896V10.3339C7.13561 10.3339 6.70836 10.17 6.39335 9.87826C6.07834 9.58651 5.90137 9.19082 5.90137 8.77822C5.90137 8.36563 6.07834 7.96994 6.39335 7.67819C6.70836 7.38644 7.13561 7.22254 7.58111 7.22254V6.83362H8.42098V7.22254C8.76839 7.22254 9.10727 7.32229 9.39094 7.50805ZM6.98723 8.22821C7.14473 8.08233 7.35836 8.00038 7.58111 8.00038V9.55607C7.35836 9.55607 7.14473 9.47412 6.98723 9.32824C6.82972 9.18237 6.74124 8.98452 6.74124 8.77822C6.74124 8.57193 6.82972 8.37408 6.98723 8.22821ZM9.01485 11.6618C8.85735 11.8076 8.64372 11.8896 8.42098 11.8896V10.3339C8.64372 10.3339 8.85735 10.4159 9.01485 10.5617C9.17236 10.7076 9.26085 10.9055 9.26085 11.1118C9.26085 11.318 9.17236 11.5159 9.01485 11.6618Z"
        fill={color}
      />
    </svg>
  );
}
