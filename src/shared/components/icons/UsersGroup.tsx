type UserGroupsProps = {
  size?: number;
  color?: string;
};

export function UsersGroup({ color = '#666666', size = 16 }: UserGroupsProps) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={size}
      height={size}
      viewBox="0 0 17 16"
      fill="none"
      color={color}
    >
      <g clipPath="url(#clip0_35035_87508)">
        <path
          d="M1.5415 13.3333V12.6667C1.5415 10.0893 3.63084 8 6.20817 8C8.7855 8 10.8748 10.0893 10.8748 12.6667V13.3333"
          stroke="currentColor"
          strokeWidth="1.5"
          strokeLinecap="round"
        />
        <path
          d="M9.5415 9.33333C9.5415 7.4924 11.0339 6 12.8748 6C14.7158 6 16.2082 7.4924 16.2082 9.33333V9.66667"
          stroke="currentColor"
          strokeWidth="1.5"
          strokeLinecap="round"
        />
        <path
          d="M6.20817 8.00033C7.6809 8.00033 8.87484 6.80639 8.87484 5.33366C8.87484 3.8609 7.6809 2.66699 6.20817 2.66699C4.73541 2.66699 3.5415 3.8609 3.5415 5.33366C3.5415 6.80639 4.73541 8.00033 6.20817 8.00033Z"
          stroke="currentColor"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M12.875 6C13.9796 6 14.875 5.10457 14.875 4C14.875 2.89543 13.9796 2 12.875 2C11.7704 2 10.875 2.89543 10.875 4C10.875 5.10457 11.7704 6 12.875 6Z"
          stroke="currentColor"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_35035_87508">
          <rect
            width="16"
            height="16"
            fill="white"
            transform="translate(0.875)"
          />
        </clipPath>
      </defs>
    </svg>
  );
}
