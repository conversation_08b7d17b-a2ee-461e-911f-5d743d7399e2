import React from 'react';

export const OtherAppsIcon: React.FC = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect width="24" height="24" rx="4" fill="#191F2E" fillOpacity="0.08" />
    <path
      d="M4 12C4 7.58172 7.58172 4 12 4C16.4183 4 20 7.58172 20 12C20 16.4183 16.4183 20 12 20C7.58172 20 4 16.4183 4 12ZM11.5 5.07655C10.8306 5.28128 10.1646 5.89722 9.61275 6.93199C9.46962 7.20034 9.33735 7.49184 9.21771 7.80361C9.92288 7.9609 10.6905 8.06112 11.5 8.09055V5.07655ZM8.24852 7.53944C8.39143 7.15535 8.55268 6.79462 8.73039 6.4614C8.90911 6.1263 9.10862 5.81241 9.32726 5.52835C8.46954 5.88297 7.69766 6.40315 7.05145 7.04904C7.41294 7.23288 7.8141 7.39777 8.24852 7.53944ZM7.50845 11.5C7.54474 10.43 7.69684 9.41263 7.94483 8.49229C7.37532 8.30701 6.84978 8.08302 6.38046 7.82544C5.6064 8.86573 5.11439 10.1283 5.01758 11.5H7.50845ZM8.90869 8.75884C8.68622 9.58644 8.54496 10.513 8.50906 11.5H11.5V9.09114C10.5902 9.06026 9.71707 8.94558 8.90869 8.75884ZM12.5 9.09114V11.5H15.4909C15.455 10.513 15.3138 9.58644 15.0913 8.75884C14.2829 8.94558 13.4098 9.06026 12.5 9.09114ZM8.50906 12.5C8.54496 13.487 8.68622 14.4135 8.90869 15.2411C9.71707 15.0544 10.5902 14.9397 11.5 14.9088V12.5H8.50906ZM12.5 12.5V14.9088C13.4098 14.9397 14.2829 15.0544 15.0913 15.2411C15.3138 14.4135 15.455 13.487 15.4909 12.5H12.5ZM9.21771 16.1964C9.33735 16.5081 9.46962 16.7996 9.61275 17.068C10.1646 18.1028 10.8306 18.7187 11.5 18.9234V15.9094C10.6905 15.9389 9.92288 16.0391 9.21771 16.1964ZM9.32726 18.4716C9.10863 18.1876 8.90911 17.8737 8.73039 17.5386C8.55268 17.2054 8.39143 16.8446 8.24852 16.4605C7.8141 16.6022 7.41294 16.7671 7.05145 16.9509C7.69766 17.5968 8.46954 18.117 9.32726 18.4716ZM7.94483 15.5077C7.69684 14.5874 7.54474 13.57 7.50845 12.5H5.01758C5.11439 13.8717 5.6064 15.1343 6.38046 16.1745C6.84978 15.917 7.37532 15.693 7.94483 15.5077ZM14.6727 18.4716C15.5305 18.117 16.3023 17.5968 16.9485 16.9509C16.5871 16.7671 16.1859 16.6022 15.7515 16.4605C15.6086 16.8446 15.4473 17.2054 15.2696 17.5386C15.0909 17.8737 14.8914 18.1876 14.6727 18.4716ZM12.5 15.9094V18.9234C13.1694 18.7187 13.8354 18.1028 14.3872 17.068C14.5304 16.7996 14.6626 16.5081 14.7823 16.1964C14.0771 16.0391 13.3095 15.9389 12.5 15.9094ZM16.0552 15.5077C16.6247 15.693 17.1502 15.917 17.6195 16.1745C18.3936 15.1343 18.8856 13.8717 18.9824 12.5H16.4915C16.4552 13.57 16.3032 14.5874 16.0552 15.5077ZM18.9824 11.5C18.8856 10.1283 18.3936 8.86572 17.6195 7.82544C17.1502 8.08302 16.6247 8.30701 16.0552 8.49229C16.3032 9.41263 16.4552 10.43 16.4915 11.5H18.9824ZM15.2696 6.4614C15.4473 6.79462 15.6086 7.15535 15.7515 7.53944C16.1859 7.39777 16.5871 7.23288 16.9485 7.04904C16.3023 6.40315 15.5305 5.88297 14.6727 5.52835C14.8914 5.81241 15.0909 6.1263 15.2696 6.4614ZM14.7823 7.80361C14.6626 7.49184 14.5304 7.20034 14.3872 6.93199C13.8354 5.89722 13.1694 5.28128 12.5 5.07655V8.09055C13.3095 8.06112 14.0771 7.9609 14.7823 7.80361Z"
      fill="#191F2E"
    />
  </svg>
);
