import * as React from 'react';

type CommentProps = {
  size?: number;
  color?: string;
};

export function Comment({ size = 24, color = '#191F2E' }: CommentProps) {
  const id = React.useId();

  return (
    <svg
      width={size}
      height={size}
      color={color}
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M14 1C14.5523 1 15 1.44772 15 2V10C15 10.5523 14.5523 11 14 11H4.41421C3.88378 11 3.37507 11.2107 3 11.5858L1 13.5858V2C1 1.44772 1.44772 1 2 1H14ZM2 0C0.895431 0 0 0.895431 0 2V14.7929C0 15.2383 0.53857 15.4614 0.853553 15.1464L3.70711 12.2929C3.89464 12.1054 4.149 12 4.41421 12H14C15.1046 12 16 11.1046 16 10V2C16 0.895431 15.1046 0 14 0H2Z"
        fill="#191F2E"
      />
      <path
        d="M3 3.5C3 3.22386 3.22386 3 3.5 3H12.5C12.7761 3 13 3.22386 13 3.5C13 3.77614 12.7761 4 12.5 4H3.5C3.22386 4 3 3.77614 3 3.5ZM3 6C3 5.72386 3.22386 5.5 3.5 5.5H12.5C12.7761 5.5 13 5.72386 13 6C13 6.27614 12.7761 6.5 12.5 6.5H3.5C3.22386 6.5 3 6.27614 3 6ZM3 8.5C3 8.22386 3.22386 8 3.5 8H8.5C8.77614 8 9 8.22386 9 8.5C9 8.77614 8.77614 9 8.5 9H3.5C3.22386 9 3 8.77614 3 8.5Z"
        fill="#191F2E"
      />
    </svg>
  );
}
