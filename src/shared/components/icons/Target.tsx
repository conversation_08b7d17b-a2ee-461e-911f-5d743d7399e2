import * as React from 'react';

type TargetProps = {
  size?: number;
  color?: string;
};

export function Target({ size = 24, color = '#ffffff' }: TargetProps) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={size}
      height={size}
      viewBox="0 0 16 16"
      fill="none"
      color={color}
    >
      <path
        d="M14.5 7.5H13.9788C13.8588 6.08715 13.243 4.76225 12.2404 3.75962C11.2377 2.75699 9.91285 2.14123 8.5 2.02125V1.5C8.5 1.36739 8.44732 1.24021 8.35355 1.14645C8.25979 1.05268 8.13261 1 8 1C7.86739 1 7.74021 1.05268 7.64645 1.14645C7.55268 1.24021 7.5 1.36739 7.5 1.5V2.02125C6.08715 2.14123 4.76225 2.75699 3.75962 3.75962C2.75699 4.76225 2.14123 6.08715 2.02125 7.5H1.5C1.36739 7.5 1.24021 7.55268 1.14645 7.64645C1.05268 7.74021 1 7.86739 1 8C1 8.13261 1.05268 8.25979 1.14645 8.35355C1.24021 8.44732 1.36739 8.5 1.5 8.5H2.02125C2.14123 9.91285 2.75699 11.2377 3.75962 12.2404C4.76225 13.243 6.08715 13.8588 7.5 13.9788V14.5C7.5 14.6326 7.55268 14.7598 7.64645 14.8536C7.74021 14.9473 7.86739 15 8 15C8.13261 15 8.25979 14.9473 8.35355 14.8536C8.44732 14.7598 8.5 14.6326 8.5 14.5V13.9788C9.91285 13.8588 11.2377 13.243 12.2404 12.2404C13.243 11.2377 13.8588 9.91285 13.9788 8.5H14.5C14.6326 8.5 14.7598 8.44732 14.8536 8.35355C14.9473 8.25979 15 8.13261 15 8C15 7.86739 14.9473 7.74021 14.8536 7.64645C14.7598 7.55268 14.6326 7.5 14.5 7.5ZM8.5 12.975V12.5C8.5 12.3674 8.44732 12.2402 8.35355 12.1464C8.25979 12.0527 8.13261 12 8 12C7.86739 12 7.74021 12.0527 7.64645 12.1464C7.55268 12.2402 7.5 12.3674 7.5 12.5V12.975C6.35305 12.858 5.28157 12.3489 4.46634 11.5337C3.65111 10.7184 3.14203 9.64695 3.025 8.5H3.5C3.63261 8.5 3.75979 8.44732 3.85355 8.35355C3.94732 8.25979 4 8.13261 4 8C4 7.86739 3.94732 7.74021 3.85355 7.64645C3.75979 7.55268 3.63261 7.5 3.5 7.5H3.025C3.14203 6.35305 3.65111 5.28157 4.46634 4.46634C5.28157 3.65111 6.35305 3.14203 7.5 3.025V3.5C7.5 3.63261 7.55268 3.75979 7.64645 3.85355C7.74021 3.94732 7.86739 4 8 4C8.13261 4 8.25979 3.94732 8.35355 3.85355C8.44732 3.75979 8.5 3.63261 8.5 3.5V3.025C9.64695 3.14203 10.7184 3.65111 11.5337 4.46634C12.3489 5.28157 12.858 6.35305 12.975 7.5H12.5C12.3674 7.5 12.2402 7.55268 12.1464 7.64645C12.0527 7.74021 12 7.86739 12 8C12 8.13261 12.0527 8.25979 12.1464 8.35355C12.2402 8.44732 12.3674 8.5 12.5 8.5H12.975C12.858 9.64695 12.3489 10.7184 11.5337 11.5337C10.7184 12.3489 9.64695 12.858 8.5 12.975ZM8 5.5C7.50555 5.5 7.0222 5.64662 6.61107 5.92133C6.19995 6.19603 5.87952 6.58648 5.6903 7.04329C5.50108 7.50011 5.45157 8.00277 5.54804 8.48773C5.6445 8.97268 5.8826 9.41814 6.23223 9.76777C6.58186 10.1174 7.02732 10.3555 7.51227 10.452C7.99723 10.5484 8.49989 10.4989 8.95671 10.3097C9.41352 10.1205 9.80397 9.80005 10.0787 9.38893C10.3534 8.9778 10.5 8.49445 10.5 8C10.5 7.33696 10.2366 6.70107 9.76777 6.23223C9.29893 5.76339 8.66304 5.5 8 5.5ZM8 9.5C7.70333 9.5 7.41332 9.41203 7.16664 9.2472C6.91997 9.08238 6.72771 8.84811 6.61418 8.57403C6.50065 8.29994 6.47094 7.99834 6.52882 7.70736C6.5867 7.41639 6.72956 7.14912 6.93934 6.93934C7.14912 6.72956 7.41639 6.5867 7.70736 6.52882C7.99834 6.47094 8.29994 6.50065 8.57403 6.61418C8.84811 6.72771 9.08238 6.91997 9.2472 7.16664C9.41203 7.41332 9.5 7.70333 9.5 8C9.5 8.39782 9.34196 8.77936 9.06066 9.06066C8.77936 9.34196 8.39782 9.5 8 9.5Z"
        fill="#191F2E"
      />
    </svg>
  );
}
