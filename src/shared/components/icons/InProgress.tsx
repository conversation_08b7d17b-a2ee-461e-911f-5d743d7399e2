import * as React from 'react';

type InProgressProps = {
  size?: number;
  color?: string;
};

export function InProgress({ size = 16, color = '#7D6F0B' }: InProgressProps) {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 16 16"
      color={color}
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M8.82165 0V1.6C12.3584 2.032 14.8639 5.224 14.4288 8.736C14.251 10.1512 13.6031 11.4671 12.5875 12.4755C11.572 13.484 10.2468 14.1274 8.82165 14.304V15.904C13.2526 15.424 16.4349 11.472 15.9515 7.08C15.5325 3.352 12.5759 0.4 8.82165 0ZM7.21039 0C5.63135 0.144 4.14094 0.76 2.91638 1.76L4.06843 2.992C4.97074 2.272 6.05834 1.808 7.21039 1.648V0ZM1.78044 2.936C0.769924 4.14796 0.149605 5.63357 0 7.2H1.61126C1.76433 6.064 2.21548 4.984 2.93249 4.08L1.78044 2.936ZM0.00805629 8.8C0.169182 10.368 0.789518 11.848 1.7885 13.064L2.93249 11.92C2.22107 11.0157 1.76752 9.93808 1.61932 8.8H0.00805629ZM4.03621 13.096L2.91638 14.192C4.13673 15.2022 5.63077 15.8312 7.21039 16V14.4C6.0643 14.2528 4.97912 13.8025 4.06843 13.096H4.03621Z"
        fill={color}
      />
    </svg>
  );
}
