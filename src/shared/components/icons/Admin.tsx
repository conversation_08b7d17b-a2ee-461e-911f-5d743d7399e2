import * as React from 'react';

type AdminProps = {
  size?: number;
  color?: string;
};

export function Admin({ size = 24, color = '#191F2E' }: AdminProps) {
  const id = React.useId();

  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={size}
      height={size}
      fill="none"
      viewBox={`0 0 ${size} ${size}`}
      color={color}
    >
      <g fill="currentColor" clipPath={`url(#${id})`}>
        <path d="M9.78645 9.87344C8.89483 9.87344 8.02325 9.60896 7.28199 9.11347C6.54073 8.61797 5.9631 7.91373 5.6222 7.08986C5.2813 6.26599 5.19244 5.3595 5.36687 4.48511C5.5413 3.61072 5.97118 2.80773 6.60212 2.17772C7.23305 1.54772 8.03669 1.11903 8.91133 0.945892C9.78598 0.772755 10.6923 0.862954 11.5157 1.20508C12.3391 1.5472 13.0425 2.12586 13.5368 2.86786C14.0312 3.60985 14.2944 4.48183 14.2931 5.37344C14.2896 6.56699 13.8134 7.71054 12.9688 8.55388C12.1243 9.39723 10.98 9.87169 9.78645 9.87344ZM9.78645 2.20011C9.15882 2.20011 8.54529 2.38622 8.02344 2.73491C7.50159 3.0836 7.09485 3.57921 6.85467 4.15906C6.61449 4.73891 6.55165 5.37696 6.67409 5.99253C6.79653 6.6081 7.09876 7.17353 7.54256 7.61733C7.98636 8.06113 8.5518 8.36336 9.16736 8.4858C9.78293 8.60824 10.421 8.5454 11.0008 8.30522C11.5807 8.06504 12.0763 7.6583 12.425 7.13645C12.7737 6.6146 12.9598 6.00107 12.9598 5.37344C12.9598 4.53182 12.6254 3.72467 12.0303 3.12956C11.4352 2.53444 10.6281 2.20011 9.78645 2.20011Z" />
        <path d="M10.9468 21.1201C10.8011 20.9743 10.6887 20.7988 10.6174 20.6054C10.546 20.4121 10.5174 20.2056 10.5335 20.0001H2.66683V16.1468C3.6121 15.1381 4.75864 14.3392 6.03222 13.8018C7.30581 13.2643 8.67807 13.0003 10.0602 13.0268H10.5402C10.5096 12.8032 10.5309 12.5757 10.6026 12.3618C10.6743 12.1479 10.7944 11.9534 10.9535 11.7934L11.0335 11.7201C10.7202 11.7201 10.3668 11.6801 10.0602 11.6801C8.43376 11.6415 6.81911 11.9648 5.33294 12.6266C3.84678 13.2884 2.52625 14.2721 1.46683 15.5068C1.38028 15.6222 1.3335 15.7625 1.3335 15.9068V20.0001C1.3335 20.3537 1.47397 20.6929 1.72402 20.9429C1.97407 21.193 2.31321 21.3334 2.66683 21.3334H11.1335L10.9468 21.1201Z" />
        <path d="M17.9136 10.8601C17.9466 10.8533 17.9806 10.8533 18.0136 10.8601C17.9805 10.8541 17.9466 10.8541 17.9136 10.8601Z" />
        <path d="M22.4532 15.5467L21.1198 15.1401C21.0245 14.8139 20.8949 14.4988 20.7332 14.2001L21.3998 12.9601C21.4215 12.9105 21.4268 12.8552 21.4148 12.8024C21.4028 12.7496 21.3742 12.7021 21.3332 12.6667L20.3665 11.7001C20.33 11.6605 20.2811 11.6344 20.2279 11.6259C20.1747 11.6174 20.1202 11.6271 20.0732 11.6534L18.8465 12.3201C18.5445 12.1503 18.2248 12.0139 17.8932 11.9134L17.4865 10.5801C17.4693 10.531 17.4365 10.4888 17.3932 10.46C17.3499 10.4311 17.2985 10.4171 17.2465 10.4201H15.8798C15.8273 10.4195 15.7761 10.4361 15.734 10.4674C15.6918 10.4987 15.6611 10.543 15.6465 10.5934L15.2398 11.9267C14.9059 12.0239 14.5839 12.158 14.2798 12.3267L13.0665 11.6601C13.0205 11.6343 12.9671 11.6249 12.915 11.6334C12.863 11.6418 12.8153 11.6677 12.7798 11.7067L11.7932 12.6667C11.7566 12.7053 11.7331 12.7543 11.7259 12.8069C11.7187 12.8596 11.7282 12.9132 11.7532 12.9601L12.4198 14.1734C12.2435 14.4739 12.1026 14.7938 11.9998 15.1267L10.6665 15.5267C10.6161 15.5413 10.5718 15.572 10.5405 15.6142C10.5092 15.6563 10.4926 15.7076 10.4932 15.7601V17.1267C10.4971 17.1749 10.5159 17.2206 10.547 17.2576C10.578 17.2947 10.6198 17.3211 10.6665 17.3334L11.9998 17.7401C12.0984 18.0671 12.2325 18.3823 12.3998 18.6801L11.7332 19.9534C11.7077 19.999 11.6979 20.0517 11.7051 20.1034C11.7123 20.1552 11.7362 20.2031 11.7732 20.2401L12.7398 21.2067C12.7776 21.2444 12.8263 21.2692 12.879 21.2776C12.9316 21.2859 12.9856 21.2775 13.0332 21.2534L14.2798 20.5867C14.5765 20.7463 14.8894 20.8737 15.2132 20.9667L15.6132 22.3001C15.6296 22.3494 15.6608 22.3925 15.7025 22.4235C15.7443 22.4545 15.7945 22.4719 15.8465 22.4734H17.2132C17.2654 22.4729 17.3162 22.4559 17.3581 22.4248C17.4 22.3936 17.431 22.3499 17.4465 22.3001L17.8532 20.9334C18.1727 20.8403 18.4811 20.7129 18.7732 20.5534L20.0332 21.2201C20.0795 21.2447 20.1326 21.2534 20.1844 21.245C20.2361 21.2366 20.2837 21.2114 20.3198 21.1734L21.3332 20.2667C21.3604 20.2276 21.375 20.1811 21.375 20.1334C21.375 20.0857 21.3604 20.0392 21.3332 20.0001L20.6665 18.7467C20.8284 18.4526 20.958 18.142 21.0532 17.8201L22.3865 17.4134C22.437 17.3988 22.4812 17.3681 22.5125 17.326C22.5438 17.2838 22.5605 17.2326 22.5598 17.1801V15.7801C22.566 15.7351 22.5592 15.6893 22.5403 15.6479C22.5214 15.6066 22.4912 15.5715 22.4532 15.5467ZM16.5665 18.6667C16.1256 18.668 15.6941 18.5384 15.3269 18.2942C14.9597 18.0501 14.6733 17.7024 14.504 17.2953C14.3346 16.8881 14.2899 16.4399 14.3757 16.0073C14.4614 15.5748 14.6736 15.1774 14.9854 14.8656C15.2972 14.5538 15.6945 14.3416 16.1271 14.2559C16.5596 14.1702 17.0079 14.2148 17.415 14.3842C17.8222 14.5535 18.1699 14.84 18.414 15.2072C18.6582 15.5744 18.7878 16.0058 18.7865 16.4467C18.7848 17.035 18.5503 17.5986 18.1343 18.0146C17.7184 18.4305 17.1548 18.665 16.5665 18.6667Z" />
      </g>
      <defs>
        <clipPath id={id}>
          <path fill="currentColor" d="M0 0H24V24H0z" />
        </clipPath>
      </defs>
    </svg>
  );
}
