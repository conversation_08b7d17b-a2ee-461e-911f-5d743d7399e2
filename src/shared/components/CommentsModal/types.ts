export interface TargetProposalCommentData {
  id: string;
  author: {
    globalId: string;
    name: string;
  };
  message: string;
  createdAt: Date;
}

export type CommentData = TargetProposalCommentData;

export interface CommentsModalRefApi {
  open: (dynamicParentId?: string) => void;
  close: () => void;
  refresh: () => Promise<void>;
  loadComments: () => Promise<void>;
}

export interface CommentsModalProps {
  parentId: string; // uuidv4
  commentType?: 'proposal' | 'target';
  title?: string;
  onClose?: () => void;
}
