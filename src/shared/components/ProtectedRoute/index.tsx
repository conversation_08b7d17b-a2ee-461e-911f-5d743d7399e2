import { useEffect } from 'react';
import { useRouter } from 'next/router';
import { useSession } from 'next-auth/react';

import { ProtectedRouteProps } from './types';

export function ProtectedRoute({
  children,
  requiredRoles = [],
  fallbackPath = '/',
}: ProtectedRouteProps) {
  const { data: session } = useSession();
  const userRoles = session?.user?.roles || [];
  const hasRequiredRoles = requiredRoles.every(role =>
    userRoles.includes(role),
  );

  const router = useRouter();

  useEffect(() => {
    if (!hasRequiredRoles) {
      void router.replace(fallbackPath);
    }
  }, [session, router, fallbackPath, hasRequiredRoles]);

  if (!hasRequiredRoles) {
    return null;
  }

  return <>{children}</>;
}
