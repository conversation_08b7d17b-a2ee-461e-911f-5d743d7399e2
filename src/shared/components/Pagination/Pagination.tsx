import React from 'react';
import {
  Container,
  Pagination as PaginationDS,
} from '@ghq-abi/design-system-v2';

import { usePagination } from './usePagination';

export function Pagination({
  currentPage,
  totalPages,
  onPageChange,
}: KpiPaginationProps) {
  const {
    showPrevious,
    showNext,
    showEllipsis,
    handlePageClick,
    getPagesToShow,
  } = usePagination(currentPage, totalPages, onPageChange);

  return (
    <Container className="flex justify-center mt-6">
      <PaginationDS.Root>
        <PaginationDS.Content>
          {showPrevious && (
            <PaginationDS.Item>
              <PaginationDS.Previous
                href="#"
                onClick={handlePageClick(currentPage - 1)}
              />
            </PaginationDS.Item>
          )}

          {getPagesToShow().map(pageNumber => (
            <PaginationDS.Item key={pageNumber}>
              <PaginationDS.Link
                href="#"
                isActive={pageNumber === currentPage}
                onClick={handlePageClick(pageNumber)}
              >
                {pageNumber}
              </PaginationDS.Link>
            </PaginationDS.Item>
          ))}

          {showEllipsis && (
            <PaginationDS.Item>
              <PaginationDS.Ellipsis />
            </PaginationDS.Item>
          )}

          {showNext && (
            <PaginationDS.Item>
              <PaginationDS.Next
                href="#"
                onClick={handlePageClick(currentPage + 1)}
              />
            </PaginationDS.Item>
          )}
        </PaginationDS.Content>
      </PaginationDS.Root>
    </Container>
  );
}
