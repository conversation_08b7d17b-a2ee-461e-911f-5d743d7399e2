import {
  Activity,
  ArrowsAngleExpand,
  ArrowUpRightSquare,
  BarChartFill,
  Calculator,
  EyeSlash,
  FileText,
  ListCheck,
  Person,
  Trophy,
} from 'react-bootstrap-icons';
import {
  Button,
  Card,
  IconWrapper,
  Tooltip,
  Typography,
} from '@ghq-abi/design-system-v2';

import { cn } from '~/shared/utils/cn';
import { CatalogListingTypeEnum } from '~/shared/utils/enums';

import { Avatar } from '../Avatar';

import type { CatalogItemCardProps } from './types';

export function CatalogItemCard({
  item,
  displayMode,
  onCatalogItemDetails,
  onOpenCatalogItemDrawer,
}: CatalogItemCardProps) {
  const {
    name,
    businessFunction,
    calculationMethod,
    deliverables,
    usage,
    frequency,
    definition,
    isActive,
    paValue,
    type,
    owners = [],
  } = item;

  const isProject = type?.includes('PROJECT');
  const icon = isProject ? <ListCheck /> : <BarChartFill />;

  return displayMode === CatalogListingTypeEnum.LIST ? (
    <Card.Root className="max-h-[76px] border group transition-(max-height) duration-[2s] rounded-[var(--theme-border-radius-pill)] border-[var(--theme-color-border-dark)] hover:max-h-[100vh] overflow-hidden">
      <Card.Content className="relative gap-4 max-h-full group-hover:max-h-[100vh] overflow-hidden">
        <div
          className={cn('grid gap-4', {
            'grid-cols-4': !isProject,
            'grid-cols-4 grid-rows-1 group-hover:grid-rows-2': isProject,
          })}
        >
          <div className="flex self-baseline gap-4 min-w-0 2xl:min-w-[285px]">
            <div className="relative">
              <IconWrapper
                variant={isProject ? 'primary' : 'secondary'}
                round="md"
                size={32}
              >
                {icon}
              </IconWrapper>
              {!isActive && (
                <EyeSlash
                  className="absolute bottom-0 right-0 text-[#7D8597] bg-white rounded-full p-0.5"
                  style={{ transform: 'translate(30%, 30%)' }}
                  size={16}
                />
              )}
            </div>
            <div className="flex flex-col min-w-0 2xl:max-w-[285px]">
              <Typography
                variant="metadata-sm-bold"
                className={cn('truncate', {
                  'text-[#7D8597]': !isActive,
                })}
              >
                {name}
              </Typography>
              <div
                className={cn('flex items-center', {
                  'gap-2': businessFunction,
                })}
              >
                <Typography variant="metadata-sm-regular" className="truncate">
                  {businessFunction}
                </Typography>
                <div className="flex items-center gap-2">
                  <Person aria-label="Person icon" />
                  <Typography variant="metadata-sm-regular">
                    {usage || 0}
                  </Typography>
                </div>
              </div>
            </div>
          </div>
          <div className="flex flex-col gap-1 min-w-0 2xl:min-w-[285px]">
            <Typography variant="metadata-sm-bold">
              {!isProject ? 'DEFINITION' : 'PROJECT OBJETIVE'}
            </Typography>
            <Tooltip.Provider>
              <Tooltip.Root>
                <Tooltip.Trigger>
                  <div className="flex items-center gap-1">
                    <FileText className="flex-shrink-0 self-baseline" />
                    <Typography
                      variant="metadata-sm-regular"
                      className={cn('truncate text-left', {
                        'group-hover:whitespace-normal': !isProject,
                      })}
                    >
                      {definition}
                    </Typography>
                  </div>
                </Tooltip.Trigger>
                <Tooltip.Content
                  className="z-[9999] break-words max-w-[90vw]"
                  side="bottom"
                  align="center"
                >
                  <Typography variant="metadata-sm-regular">
                    {definition}
                  </Typography>
                </Tooltip.Content>
              </Tooltip.Root>
            </Tooltip.Provider>
          </div>
          <div className="flex flex-col gap-1 min-w-0 2xl:min-w-[285px]">
            <Typography variant="metadata-sm-bold">
              {!isProject ? 'CALCULATION METHOD' : 'DELIVERABLES COUNT'}
            </Typography>
            <Tooltip.Provider>
              <Tooltip.Root>
                <Tooltip.Trigger>
                  <div className="flex items-center gap-2 truncate">
                    {!isProject ? (
                      <Calculator className="flex-shrink-0 self-baseline" />
                    ) : (
                      <Trophy className="flex-shrink-0 self-baseline" />
                    )}
                    <Typography
                      variant="metadata-sm-regular"
                      className="truncate text-left group-hover:whitespace-normal"
                    >
                      {!isProject ? calculationMethod : deliverables?.length}
                    </Typography>
                  </div>
                </Tooltip.Trigger>
                <Tooltip.Content
                  className="z-[9999] break-words max-w-[90vw]"
                  side="bottom"
                  align="center"
                >
                  <Typography variant="metadata-sm-regular">
                    {!isProject ? calculationMethod : deliverables?.length}
                  </Typography>
                </Tooltip.Content>
              </Tooltip.Root>
            </Tooltip.Provider>
          </div>
          <div className="flex justify-end w-auto ">
            <Button
              size="icon"
              variant="tertiary"
              aria-label="Open details"
              onClick={() => onOpenCatalogItemDrawer?.(item.uid)}
            >
              <ArrowUpRightSquare className="text-lg " />
            </Button>
            <Button
              size="icon"
              variant="tertiary"
              aria-label="Expand"
              onClick={() => item.uid && onCatalogItemDetails?.(item.uid)}
              disabled={!item.uid}
            >
              <ArrowsAngleExpand className="text-lg" />
            </Button>
          </div>
          <div className="hidden group-hover:block pl-[50px]">
            <Typography variant="metadata-sm-bold">OWNER</Typography>
            {owners.length > 0 && (
              <div className="w-[60px] h-[32px] flex">
                {owners?.map((owner, index) => (
                  <Avatar
                    className={cn('border-[2px] border-white', {
                      'relative left-[-10px]': index > 0,
                    })}
                    size={32}
                    key={index}
                    name={owner.name}
                    globalId={owner.globalId.toString()}
                  />
                ))}
              </div>
            )}
          </div>

          {isProject && (
            <div className="hidden group-hover:block flex flex-col gap-1 truncate">
              <Typography className="block" variant="metadata-sm-bold">
                FREQUENCY
              </Typography>
              <Tooltip.Provider>
                <Tooltip.Root>
                  <Tooltip.Trigger>
                    <div className="flex items-center gap-1">
                      <Activity className="flex-shrink-0 self-baseline" />
                      <Typography
                        variant="metadata-sm-regular"
                        className="truncate text-left group-hover:whitespace-normal"
                      >
                        {frequency}
                      </Typography>
                    </div>
                  </Tooltip.Trigger>
                  <Tooltip.Content
                    className="z-[9999] break-words max-w-[90vw]"
                    side="bottom"
                    align="center"
                  >
                    <Typography variant="metadata-sm-regular">
                      {frequency}
                    </Typography>
                  </Tooltip.Content>
                </Tooltip.Root>
              </Tooltip.Provider>
            </div>
          )}
        </div>

        {isProject && (
          <div className="hidden group-hover:block">
            <div className="grid grid-cols-5 gap-2 mt-[16px] pl-[50px]">
              {deliverables?.map((deliverable, index) => (
                <div
                  key={index}
                  className="border rounded-[8px] px-[16px] py-[8px] leading-none"
                >
                  <Typography className="truncate" variant="metadata-sm-bold">
                    {deliverable.name}
                  </Typography>
                </div>
              ))}
            </div>
          </div>
        )}
      </Card.Content>
    </Card.Root>
  ) : (
    <Card.Root className="h-[70px] border-none group">
      <Card.Content className="grid gap-16 transition-(max-height) duration-[2s] rounded-[var(--theme-border-radius-pill)] border-[var(--theme-color-border-dark)] gap-4 border bg-white group-hover:relative group-hover:z-[1] max-h-full group-hover:max-h-[100vh] overflow-hidden">
        <div className="grid grid-cols-2">
          <div className="flex gap-4 min-w-0 2xl:min-w-[285px] items-center">
            <div className="relative">
              <IconWrapper
                variant={isProject ? 'primary' : 'secondary'}
                round="md"
                size={32}
              >
                {icon}
              </IconWrapper>
              {!isActive && (
                <EyeSlash
                  className="absolute bottom-0 right-0 text-[#7D8597] bg-white rounded-full p-0.5"
                  style={{ transform: 'translate(30%, 30%)' }}
                  size={16}
                />
              )}
            </div>
            <div className="flex flex-col max-w-[130px] 2xl:max-w-full">
              <Typography
                variant="metadata-sm-bold"
                className={cn('truncate', {
                  'text-[#7D8597]': !isActive,
                })}
              >
                {name}
              </Typography>
              <div
                className={cn('flex items-center', {
                  'gap-2': businessFunction,
                })}
              >
                <Typography variant="metadata-sm-regular" className="truncate">
                  {businessFunction}
                </Typography>
                <div className="flex items-center gap-2">
                  <Person aria-label="Person icon" />
                  <Typography variant="metadata-sm-regular">
                    {usage || 0}
                  </Typography>
                </div>
              </div>
            </div>
          </div>
          <div className="flex justify-end w-auto">
            <Button
              size="icon"
              variant="tertiary"
              aria-label="Open details"
              onClick={() => onOpenCatalogItemDrawer?.(item.uid)}
            >
              <ArrowUpRightSquare className="text-lg " />
            </Button>
            <Button
              size="icon"
              variant="tertiary"
              aria-label="Expand"
              onClick={() => item.uid && onCatalogItemDetails?.(item.uid)}
              disabled={!item.uid}
            >
              <ArrowsAngleExpand className="text-lg" />
            </Button>
          </div>
        </div>

        <div className="hidden group-hover:block">
          <Typography variant="metadata-sm-regular">{definition}</Typography>
          {isProject && (
            <div className="grid grid-cols-1 gap-2">
              {deliverables?.map((deliverable, index) => (
                <div
                  key={index}
                  className="border rounded-[8px] px-[16px] py-[8px] leading-none"
                >
                  <Typography className="truncate" variant="metadata-sm-bold">
                    {deliverable.name}
                  </Typography>
                </div>
              ))}
            </div>
          )}
          {owners.length > 0 && (
            <div className="h-[32px] mt-[16px] flex items-center">
              {owners.map((owner, index) => (
                <Avatar
                  className={cn('border-[2px] border-white', {
                    '-ml-2': index > 0,
                  })}
                  size={32}
                  key={index}
                  name={owner.name}
                  globalId={owner.globalId.toString()}
                />
              ))}
            </div>
          )}
        </div>
      </Card.Content>
    </Card.Root>
  );
}
