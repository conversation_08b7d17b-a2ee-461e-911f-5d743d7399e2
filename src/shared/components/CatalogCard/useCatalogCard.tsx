import { useCallback, useEffect, useRef, useState } from 'react';

import { DeliverableItem } from '~/shared/types/Deliverable';

export function useCatalogCard(item: DeliverableItem) {
  const { name, businessFunction, definition, calculationMethod = [] } = item;

  const [hovered, setHovered] = useState(false);
  const mainContentRef = useRef<HTMLDivElement>(null);
  const [mainContentHeight, setMainContentHeight] = useState(0);

  useEffect(() => {
    if (mainContentRef.current) {
      setMainContentHeight(mainContentRef.current.offsetHeight);
    }
  }, [item, name, hovered]);

  const handleMouseEnter = useCallback(() => setHovered(true), []);
  const handleMouseLeave = useCallback(() => setHovered(false), []);

  return {
    name,
    businessFunction,
    definition,
    calculationMethod,
    hovered,
    setHovered,
    mainContentRef,
    mainContentHeight,
    handleMouseEnter,
    handleMouseLeave,
  };
}
