import { GetServerSidePropsContext, GetServerSidePropsResult } from 'next';
import { JWT } from 'next-auth/jwt';

import { withSSRSession } from '~/app/hocs/withSSRSession';

export function withAdminRoute<
  P extends Record<string, unknown> = Record<string, unknown>,
>(
  getServerSidePropsFunc?: (
    context: GetServerSidePropsContext,
    session: JWT | null,
  ) => Promise<GetServerSidePropsResult<P>>,
  redirectTo: string = '/',
) {
  return withSSRSession(async (context, session) => {
    const user = session?.user.roles;
    const isGlobalAdmin = user?.isGlobalAdmin ?? false;

    if (!isGlobalAdmin) {
      return {
        redirect: {
          destination: redirectTo,
          permanent: false,
        },
      };
    }

    if (getServerSidePropsFunc) {
      return getServerSidePropsFunc(context, session);
    }

    return {
      props: {} as P,
    };
  });
}

export const withAdminProtection = () => withAdminRoute();
