import { useSession } from 'next-auth/react';

export function useUser() {
  const { data: session } = useSession();

  const user = session?.user;

  return {
    name: user?.name,
    zone: user?.zone,
    globalId: user?.globalId,
    employeeId: user?.employeeId,
    managedZones: user?.managedZones,
    roles: user?.roles,
    language: user?.language,
    persona: user?.persona,
    isProxying: user?.preferredLanguage,
    band: user?.band,
    isGlobalAdmin: user?.roles?.isGlobalAdmin ?? false,
  };
}

export type User = ReturnType<typeof useUser>;
