import { useAbility } from '@casl/react';

import { AbilityContext } from '~/app/contexts/AbilityContext';
import { AppAbility } from '~/shared/auth/permissions';

import { UserPermissionsEnum } from '../utils/enums';

export function usePermissions() {
  const ability = useAbility<AppAbility>(AbilityContext);
  // const isGlobalAdmin = ability.can('manage', 'Admin');

  return {
    ability,
    // isGlobalAdmin,

    can: (action: string, subject: string) =>
      ability.can(action as any, subject as any),
  };
}

export type UsePermissions = ReturnType<typeof usePermissions>;
