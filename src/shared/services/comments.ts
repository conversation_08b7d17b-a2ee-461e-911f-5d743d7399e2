import axios from 'axios';

const BASE_URL = process.env.NEXT_PUBLIC_API_URL;

export interface ProposalComments {
  id: string;
  author: {
    globalId: string;
    name: string;
  };
  message: string;
  createdAt: Date;
}

export interface CreateProposalCommentDto {
  comment: string;
  createdBy: string;
}

export interface ProposalCommentsListResponse {
  data: ProposalComments[];
  pageNumber?: number;
  pageSize?: number;
  totalRecords?: number;
}

class ProposalCommentsService {
  async getComments(
    proposalId: string,
    pageNumber: number = 1,
    pageSize: number = 100,
  ): Promise<ProposalCommentsListResponse> {
    try {
      const response = await axios.get<ProposalCommentsListResponse>(
        `${BASE_URL}/proposal-comments/${proposalId}?pageNumber=${pageNumber}&pageSize=${pageSize}`,
      );
      return (
        response.data || {
          data: [],
          pageNumber: 1,
          pageSize: 100,
          totalRecords: 0,
        }
      );
    } catch (error) {
      throw error;
    }
  }

  async createComment(
    proposalId: string,
    commentData: CreateProposalCommentDto,
  ): Promise<ProposalComments> {
    try {
      const response = await axios.post<ProposalComments>(
        `${BASE_URL}/proposal-comments/${proposalId}`,
        commentData,
      );
      return response.data;
    } catch (error) {
      throw error;
    }
  }

  async deleteComment(commentId: string): Promise<void> {
    try {
      await axios.delete(`${BASE_URL}/proposal-comments/${commentId}`);
    } catch (error) {
      throw error;
    }
  }
}

const proposalCommentsService = new ProposalCommentsService();
export default proposalCommentsService;
