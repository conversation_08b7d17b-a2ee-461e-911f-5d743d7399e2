import axios from 'axios';

import { Employee } from '../types/Employee';
import { EmployeeServiceInterface } from '../types/EmployeeService';
import { EmployeesResponse } from '../types/EmployeesResponse';

const BASE_URL = process.env.NEXT_PUBLIC_API_URL;

class EmployeeService implements EmployeeServiceInterface {
  async searchEmployees(query: string): Promise<EmployeesResponse> {
    const response = await axios.get(
      `${BASE_URL}/employees?search=${query}&pageSize=10&pageNumber=1`,
    );
    return response.data;
  }

  async getEmployeeById(id: string): Promise<Employee | null> {
    const response = await axios.get(`${BASE_URL}/employees/${id}`);
    return response.data;
  }
}

const employeeService = new EmployeeService();
export default employeeService;
