import axios from 'axios';

import { DeliverableItem } from '../types/Deliverable';
import {
  DeliverableItemsResponse,
  IDeliverablesService,
} from '../types/DeliverablesService';
import { GetDeliverableFilters } from '../types/GetDeliverableFilters';

const BASE_URL = process.env.NEXT_PUBLIC_API_URL_KPI_CATALOG;

const withRetry = async <T>(
  fn: () => Promise<T>,
  retries: number = 3,
  delay: number = 1000,
): Promise<T> => {
  let lastError: any;

  for (let i = 0; i <= retries; i++) {
    try {
      return await fn();
    } catch (error: any) {
      lastError = error;

      if (error?.response?.status === 404 || error?.response?.status === 403) {
        throw error;
      }

      if (i < retries) {
        await new Promise(resolve => setTimeout(resolve, delay * (i + 1)));
      }
    }
  }

  throw lastError;
};

export class DeliverablesService implements IDeliverablesService {
  async getDeliverables(
    filters: GetDeliverableFilters,
  ): Promise<DeliverableItemsResponse> {
    const params = new URLSearchParams();

    params.append('pageSize', filters.pageSize.toString());
    params.append('pageNumber', filters.pageNumber.toString());

    if (filters.search) {
      params.append('search', filters.search);
    }

    if (filters.businessFunctions && filters.businessFunctions.length > 0) {
      filters.businessFunctions.forEach(func => {
        params.append('businessFunctions', func);
      });
    }

    if (filters.orderBy) {
      params.append('orderBy', filters.orderBy);
    }

    if (filters.isActive !== undefined) {
      params.append('isActive', filters.isActive.toString());
    }

    if (filters.deliverableTypes && filters.deliverableTypes.length > 0) {
      filters.deliverableTypes.forEach(type => {
        params.append('deliverableTypes', type);
      });
    }

    const response = await axios.get<DeliverableItemsResponse>(
      `${BASE_URL}/deliverables?${params.toString()}`,
    );

    return response.data;
  }

  async getDeliverableById(uid: string): Promise<DeliverableItem> {
    return withRetry(async () => {
      const response = await axios.get(`${BASE_URL}/deliverables/${uid}`, {
        timeout: 15000,
        headers: {
          'Content-Type': 'application/json',
        },
      });

      return response.data;
    });
  }
}

const deliverablesService = new DeliverablesService();
export default deliverablesService;
