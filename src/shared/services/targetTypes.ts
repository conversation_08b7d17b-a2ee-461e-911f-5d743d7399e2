import axios from 'axios';

const BASE_URL = `${process.env.NEXT_PUBLIC_API_URL}/target-type`;

interface TargetTypeRequestBody {
  targetUids: string[];
  agree?: boolean;
}

class TargetTypesService {
  async addFeedbackToTargets(targetUids: string[], agree?: boolean) {
    const requestBody: TargetTypeRequestBody = {
      targetUids,
      ...(agree !== undefined && { agree }),
    };
    const response = await axios.post(`${BASE_URL}/feedback`, requestBody);
    return response.data;
  }

  async addFinalToTargets(targetUids: string[], agree?: boolean) {
    const requestBody: TargetTypeRequestBody = {
      targetUids,
      ...(agree !== undefined && { agree }),
    };
    const response = await axios.post(`${BASE_URL}/final`, requestBody);
    return response.data;
  }
}

const targetTypesService = new TargetTypesService();

export default targetTypesService;
