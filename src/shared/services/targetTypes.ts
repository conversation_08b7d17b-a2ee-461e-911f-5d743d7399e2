import axios from 'axios';

const BASE_URL = `${process.env.NEXT_PUBLIC_API_URL}/target-type`;

class TargetTypesService {
  async addFeedbackToTargets(targetUids: string[]) {
    const response = await axios.post(`${BASE_URL}/feedback`, targetUids);
    return response.data;
  }

  async addFinalToTargets(targetUids: string[]) {
    const response = await axios.post(`${BASE_URL}/final`, targetUids);
    return response.data;
  }
}

const targetTypesService = new TargetTypesService();

export default targetTypesService;
