import axios from 'axios';

const BASE_URL = process.env.NEXT_PUBLIC_API_URL_KPI_CATALOG;

export type BusinessFunctionDto = {
  code: string;
  label: string;
};

export type BusinessFunctionsListResponse = {
  data: BusinessFunctionDto[];
  pageNumber?: number;
  pageSize?: number;
  totalRecords?: number;
};

class BusinessFunctionsService {
  async getBusinessFunctions(): Promise<BusinessFunctionDto[]> {
    const response = await axios.get<BusinessFunctionsListResponse>(
      `${BASE_URL}/business-functions`,
    );
    return response.data?.data ?? [];
  }
}

const businessFunctionsService = new BusinessFunctionsService();
export default businessFunctionsService;
