import { ReactNode } from 'react';
import { Box, CSS, Flex, Skeleton, styled, Text } from '@ghq-abi/design-system';

import { Footer } from '~/app/components';
import { PageHeading, Spinner } from '~/shared/components';

// TODO REFACTOR: Remove this component and use the new ContentPage component
type ContentPageTemplateProps = {
  children: React.ReactNode;
  title?: string;
  titleHelperText?: string | number;
  leftAddon?: ReactNode;
  rightAddon?: ReactNode;
  subtitle?: string;
  microtitle?: string;
  meetingId?: string;
  controls?: React.ReactNode;
  showSpinner?: boolean;
  transparentContent?: boolean;
  css?: CSS;
  isLoading?: boolean;
  contentCss?: CSS;
  headerCss?: CSS;
  footerCss?: CSS;
  hideFooter?: boolean;
};

const ContentFlex = styled(Flex, {
  backgroundColor: '$white',
  borderRadius: '$3',
  // TODO: UX - Replace with var after DS update
  p: '$md',
  flex: 1,
  border: '1px solid #EEEFF2',

  '@md': {
    px: '$3',
    py: '$md',
  },

  variants: {
    transparent: {
      true: {
        backgroundColor: 'transparent',
        border: 'none',
        boxShadow: 'none',
      },
    },
  },
});

export function ContentPage({
  children,
  title,
  titleHelperText,
  leftAddon,
  rightAddon,
  subtitle,
  microtitle,
  meetingId,
  controls = null,
  showSpinner,
  transparentContent,
  css,
  isLoading,
  contentCss,
  headerCss,
  footerCss,
  hideFooter,
}: ContentPageTemplateProps) {
  const hasSubcontent = subtitle || microtitle || meetingId;

  return (
    <Flex
      direction="column"
      css={{
        height: '$full',
        padding: '0px',
        gap: '$md',
        '@lg': { pt: '$md', px: '$md' },
        ...css,
      }}
    >
      <Flex
        direction={hasSubcontent ? 'column' : 'row'}
        justify="between"
        wrap="wrap"
        gap="md"
        css={headerCss}
      >
        {leftAddon && leftAddon}
        {title && (
          <PageHeading
            css={{
              fontSize: '$4',
              m: 0,
              display: 'inline-block',
              fontWeight: '$medium',
              color: '#191F2E',
              '@lg': {
                fontSize: '$7',
                lineHeight: '$8',
              },
            }}
          >
            {title}

            {titleHelperText && (
              <Text
                as="span"
                css={{ verticalAlign: 'middle', fontSize: '$md', ml: '$3' }}
              >
                {titleHelperText}
              </Text>
            )}
            {showSpinner && <Spinner css={{ height: '$6', width: '$6' }} />}
          </PageHeading>
        )}
        {rightAddon && rightAddon}
        <Flex justify="between" align="center" wrap="wrap" gap="md">
          <Flex
            align="baseline"
            gap="lg"
            wrap="wrap"
            css={{ '&:empty': { display: 'none' } }}
          >
            {isLoading && (
              <Skeleton height={24} width={300} css={{ mt: '$md' }} />
            )}

            {subtitle && !meetingId && !isLoading ? (
              <Box
                css={{
                  appearance: 'none',
                  lineHeight: '3rem',
                  border: 0,
                  backgroundColor: 'transparent',
                  fontSize: '$2xl',
                  color: '$gray750',
                  fontWeight: '$medium',
                  display: 'flex',
                  gap: '$xs',
                  svg: {
                    transform: 'translateY(8px)',
                  },
                }}
              >
                {subtitle}
              </Box>
            ) : null}

            {isLoading && <Skeleton height={24} css={{ mt: '$md' }} />}
            {microtitle && !isLoading ? (
              <Text
                css={{
                  fontSize: '$md',
                  fontWeight: '$medium',
                  lineHeight: '3rem',
                }}
              >
                {microtitle}
              </Text>
            ) : null}
          </Flex>
          <Flex gap="md" wrap="wrap" css={{ '&:empty': { display: 'none' } }}>
            {controls}
          </Flex>
        </Flex>
      </Flex>
      <ContentFlex
        transparent={transparentContent}
        direction="column"
        css={contentCss}
      >
        {children}
      </ContentFlex>
      <Footer css={footerCss} hidden={hideFooter} />
    </Flex>
  );
}
