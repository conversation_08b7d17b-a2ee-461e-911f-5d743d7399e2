import React from 'react';
import { Box, useResponsive } from '@ghq-abi/design-system';

import { DesktopMenu, ErrorBoundary, Header } from '~/app/components';
import { useStellarWidget } from '~/shared/hooks/useStellarWidget';

import { StyledContent, StyledWrapper } from './styles';

type AppTemplateProps = {
  children: React.ReactNode;
};

export function AppTemplate({ children }: AppTemplateProps) {
  useStellarWidget();

  const { isMobile } = useResponsive({ useOnlyAbiTokens: true });

  return (
    <>
      <Header />

      <StyledWrapper>
        {!isMobile && <DesktopMenu />}

        <StyledContent as="main">
          <ErrorBoundary inApp>
            <Box css={{ flex: 1 }}>{children}</Box>
          </ErrorBoundary>
        </StyledContent>
      </StyledWrapper>
    </>
  );
}
