import Image from 'next/image';

import abiLogo from '~/../public/img/abi_logo_old.png';

import { StyledCard, StyledWrapper } from './styles';

type AuthErrorTemplateProps = {
  children: React.ReactNode;
};

export function AuthErrorTemplate({ children }: AuthErrorTemplateProps) {
  return (
    <StyledWrapper justify="center" align="center">
      <StyledCard direction="column" justify="center" align="center">
        <Image src={abiLogo} placeholder="blur" alt="AB InBev" />
        {children}
      </StyledCard>
    </StyledWrapper>
  );
}
