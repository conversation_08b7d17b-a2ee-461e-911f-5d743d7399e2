import { AppProps } from 'next/app';
import { SessionProviderProps } from 'next-auth/react';

import { AppAbility } from '~/shared/auth/permissions';

export type RouteObject = {
  key: string;
  title?: string;
  icon?: JSX.Element;
  path: string;
  routeComponents?: RouteObject[];
};

export type CustomAppProps = {
  Component: AppProps['Component'] & { unauth?: boolean };
  pageProps: AppProps['pageProps'] & {
    session: SessionProviderProps['session'];
    dehydratedState?: unknown;
    abilityRules?: AppAbility['_indexedRules'];
  };
};

export type MenuItem = Omit<RouteObject, 'icon' | 'routeComponents'> & {
  icon?: string;
  placement: 'BODY' | 'HEADER' | 'FOOTER' | 'HIDDEN';
  routeComponents?: Array<MenuItem>;
};
