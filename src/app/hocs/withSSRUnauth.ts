import type {
  GetServerSideProps,
  GetServerSidePropsContext,
  GetServerSidePropsResult,
} from 'next';
import { AUTH_ERRORS } from '@ghq-abi/auth-client-lib';

import { getServerSessionWrapper } from '~/pages/api/auth/[...nextauth]';

const basePath = process.env.NEXT_PUBLIC_BASE_PATH || '';

export function withSSRUnauth<T extends Record<string, unknown>>(
  fn?: GetServerSideProps<T>,
) {
  return async (
    context: GetServerSidePropsContext,
  ): Promise<GetServerSidePropsResult<T>> => {
    const session = await getServerSessionWrapper(context.req, context.res);

    const isErrorPage = context.resolvedUrl.includes('auth/error');

    if (session?.error === AUTH_ERRORS.REFRESH_TOKEN_ERROR && isErrorPage) {
      return {
        redirect: {
          permanent: false,
          destination: `${basePath}/auth/redirect`,
        },
      };
    }

    if (!session?.error && session?.user) {
      return {
        redirect: { permanent: false, destination: `${basePath}/home` },
      };
    }

    if (!fn) {
      return {
        props: {} as T,
      };
    }

    return fn(context);
  };
}
