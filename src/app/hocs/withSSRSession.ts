import type { GetServerSidePropsContext, GetServerSidePropsResult } from 'next';
import { JWT } from 'next-auth/jwt';
import { AUTH_ERRORS, AuthSession } from '@ghq-abi/auth-client-lib';

import { getServerSessionWrapper } from '~/pages/api/auth/[...nextauth]';
import { createUserAbility } from '~/shared/auth/permissions';

import { type Flags, mountFlags } from '../contexts/FlagsContext';

function checkIfIsMobileDevice(userAgent: string) {
  return /iPhone|iPad|iPod|Android/i.test(userAgent);
}

async function fetchAndEnhanceSession(session: AuthSession<any> | null) {
  if (!session) {
    return null;
  }

  const userSession = {
    ...session,
  };

  // Remove sensitive data before returning
  delete userSession?.user?.token;
  delete userSession?.user?.refreshToken;
  delete userSession?.user?.proxiedAs?.token;
  delete userSession?.user?.proxiedAs?.refreshToken;

  return userSession;
}

const basePath = process.env.NEXT_PUBLIC_BASE_PATH || '';

export function withSSRSession<T extends Record<string, unknown>>(
  fn?: (
    context: GetServerSidePropsContext,
    session: JWT | null,
  ) => Promise<GetServerSidePropsResult<T>>,
) {
  return async <
    Q extends {
      session: JWT | null;
      isMobile: boolean;
      flags: Flags;
      userNonce?: string;
    } & T,
  >(
    context: GetServerSidePropsContext,
  ): Promise<GetServerSidePropsResult<Q>> => {
    try {
      // Retrieve session via NextAuth
      const session = await getServerSessionWrapper(context.req, context.res);

      if (session?.error === AUTH_ERRORS.REFRESH_TOKEN_ERROR) {
        return {
          redirect: {
            permanent: false,
            destination: `${basePath}/auth/redirect`,
          },
        };
      }

      if (session?.error === AUTH_ERRORS.NO_REDIS_SESSION) {
        return {
          redirect: {
            destination: `${basePath}/auth/error`,
            permanent: false,
          },
        };
      }

      // Enhance session with abilities and proxy data
      const userSession = await fetchAndEnhanceSession(session);

      if (!userSession) {
        return {
          redirect: {
            destination: `${basePath}/auth/redirect`,
            permanent: false,
          },
        };
      }

      // Check for blocked access
      const ability = createUserAbility(userSession?.user);

      // Determine mobile status and flags
      const isMobile = checkIfIsMobileDevice(
        context.req.headers['user-agent'] ?? '',
      );
      const flags = mountFlags(userSession?.user?.flags);

      // Execute additional logic if provided
      if (!fn) {
        return {
          props: {
            session: userSession,
            isMobile,
            flags,
          } as unknown as Q,
        };
      }

      const result = await fn(context, userSession);

      if ('props' in result) {
        return {
          ...result,
          props: {
            ...result.props,
            session: userSession,
            isMobile,
            flags,
          } as unknown as Q,
        };
      }

      return result;
    } catch (error) {
      console.error('Error in withSSRSession:', error);
      return {
        redirect: {
          destination: `${basePath}/auth/error`,
          permanent: false,
        },
      };
    }
  };
}
