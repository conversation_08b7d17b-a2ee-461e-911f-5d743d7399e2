import * as React from 'react';
import { useLocation } from 'react-use';
import { useTranslate } from '@tolgee/react';

import {
  CheersHubIcon,
  OtherAppsIcon,
  OurAppsIcon,
} from '~/shared/components/icons';

import { useMenuRoutes } from '../components/Nav/hooks/useMenuRoutes';
import { RouteObject } from '../types';

type NavContextData = {
  isAppMenuOpen: boolean;
  subMenuOpen?: RouteObject | null;
  menuHeaderItem: RouteObject;
  menuItems: RouteObject[];
  footerItems: RouteObject[];
  toggleAppMenu: () => void;
  toggleSubMenu: (item: RouteObject | null) => void;
  checkIfIsActive: (menuRoute: string, subitems?: RouteObject[]) => boolean;
};

const NavContext = React.createContext({} as NavContextData);

type NavProviderProps = {
  children: React.ReactNode;
};

export function NavProvider({ children }: NavProviderProps) {
  const { t } = useTranslate();

  const location = useLocation();
  const [isAppMenuOpen, setIsAppMenuOpen] = React.useState(false);
  const [subMenuOpen, setSubmenuOpen] = React.useState<RouteObject | null>();
  const { menuRoutes, footerRoutes } = useMenuRoutes();

  const rootUrl = process.env.NEXT_PUBLIC_APP_URL || '';

  const toggleSubMenu = (item: RouteObject | null) => {
    setSubmenuOpen(item?.routeComponents?.length ? item : null);
  };

  const checkIfIsActive = React.useCallback(
    (menuRoute: string) =>
      !!menuRoute &&
      (menuRoute === location.pathname ||
        menuRoute.replace(rootUrl, '').replace('/', '') ===
          location.pathname?.split('/')[1]),
    [location.pathname],
  );

  const ourApps: RouteObject[] = React.useMemo(() => {
    const values: RouteObject[] = [
      {
        key: 'compensation',
        title: 'Compensation',
        path: process.env.NEXT_PUBLIC_MY_COMPENSATION_URL || '',
        icon: <OtherAppsIcon />,
      },
      {
        key: 'lcm',
        title: 'LCM',
        path: process.env.NEXT_PUBLIC_LCM_URL || '',
        icon: <OtherAppsIcon />,
      },
      {
        key: 'opr',
        title: 'OPR',
        path: process.env.NEXT_PUBLIC_MY_TALENT_CARD_URL || '',
        icon: <OtherAppsIcon />,
      },
      {
        key: 'workday',
        title: 'Workday',
        path: process.env.NEXT_PUBLIC_WORKDAY_URL || '',
        icon: <OtherAppsIcon />,
      },
      {
        key: 'cheersHub',
        title: 'CheersHub',
        path: process.env.NEXT_PUBLIC_CHEERSHUB_URL || '',
        icon: <CheersHubIcon />,
      },
    ];

    return values;
  }, []);

  function toggleAppMenu() {
    setIsAppMenuOpen(state => !state);
  }

  const providerValue = React.useMemo(
    () => ({
      isAppMenuOpen,
      subMenuOpen,
      menuItems: menuRoutes,
      footerItems: footerRoutes,
      menuHeaderItem: {
        key: 'our_apps',
        title: t('menu_our_apps'),
        path: '/',
        icon: <OurAppsIcon />,
        routeComponents: ourApps,
      },
      toggleAppMenu,
      toggleSubMenu,
      checkIfIsActive,
    }),
    [
      isAppMenuOpen,
      subMenuOpen,
      menuRoutes,
      footerRoutes,
      t,
      ourApps,
      checkIfIsActive,
    ],
  );

  return (
    <NavContext.Provider value={providerValue}>{children}</NavContext.Provider>
  );
}

export function useNav() {
  const context = React.useContext(NavContext);

  if (!context) {
    throw new Error('useNav must be used within a NavProvider');
  }

  return context;
}
