import { styled } from '@ghq-abi/design-system';

export const StyledHeader = styled('header', {
  height: '$appHeaderHeight',
  width: '$full',
  backgroundColor: '$white',
  borderBottom: '1px solid $gray410',
  px: '$4',
  zIndex: '$sticky',

  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  gap: '$md',

  position: 'fixed',
  top: 0,

  '@tabletSm': {
    px: '$6',
  },

  variants: {
    isMenuOpen: {
      true: {
        '@tabletSm': {
          left: '$appMenuOpenWidth',
          width:
            'calc($full - $appMenuOpenWidth - var(--removed-body-scroll-bar-size, 0px))',
        },
      },
      false: {
        '@tabletSm': {
          left: '$appMenuClosedWidth',
          width:
            'calc($full - $appMenuClosedWidth - var(--removed-body-scroll-bar-size, 0px))',
        },
      },
    },
  },
});
