import { useMutation } from '@tanstack/react-query';
import { useTolgee, useTranslate } from '@tolgee/react';

import { useToast } from '~/app/contexts/ToastContext';
import { northstarUserApi } from '~/shared/services/api';

function updateLanguage(languageId: string) {
  return northstarUserApi().post(`set-preferred-language`, {
    str_lang_code: languageId,
  });
}

export function useUpdateLanguage() {
  const { t } = useTranslate();
  const toast = useToast();
  const tolgee = useTolgee();

  return useMutation({
    mutationFn: updateLanguage,
    onMutate: async (newLanguage: string) => {
      await tolgee.changeLanguage(newLanguage);
      toast.add({
        title: t('common_preferred_language_updating_title'),
        description: t('common_preferred_language_updating'),
        type: 'neutral',
      });
    },
    onSuccess: async (_data, newLanguage) => {
      await tolgee.changeLanguage(newLanguage);
      toast.add({
        type: 'success',
        title: t('common_preferred_language_updated_title'),
        description: t('common_preferred_language_success'),
      });
    },
    onError: async (_error, newLanguage) => {
      await tolgee.changeLanguage(newLanguage);
      toast.add({
        type: 'error',
        title: t('common_preferred_language_error_title'),
        description: t('common_preferred_language_error'),
      });
    },
  });
}
