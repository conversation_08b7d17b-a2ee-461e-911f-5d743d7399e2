import React from 'react';
import { LanguageDropdown } from '@ghq-abi/design-system';
import { useTranslate } from '@tolgee/react';

import { normalizeLanguage } from '~/shared/utils/string';

import { useLanguageDropdown } from './hooks';

type LanguageDropdownProps = {
  currentLanguage?: string;
};

export const LanguageDropdownHeader = ({
  currentLanguage,
}: LanguageDropdownProps) => {
  const { t } = useTranslate();
  const { handleLanguageChange } = useLanguageDropdown();

  const optionsLanguages: { [key: string]: string } = {
    'en-US': t('common_english'),
    'fr-FR': t('common_french'),
    es: t('common_spanish'),
    'pt-BR': t('common_portuguese'),
    'ru-RU': t('common_russian'),
    'uk-UA': t('common_ukrainian'),
    'nl-NL': t('common_dutch'),
    'de-DE': t('common_german'),
    'it-IT': t('common_italian'),
    'zh-CN': t('common_chinese'),
    'ko-KR': t('common_korean'),
    'ja-JP': t('common_japanese'),
    'vi-VN': t('common_vietnamese'),
  };

  const languageOptions = Object.keys(optionsLanguages).map(lang => ({
    label: optionsLanguages[lang],
    onSelect: () => handleLanguageChange(lang),
  }));

  const displayLanguage = normalizeLanguage(currentLanguage);

  const userLanguageLabel = t(
    optionsLanguages[displayLanguage] ?? 'common_english',
  );

  return <LanguageDropdown value={userLanguageLabel} items={languageOptions} />;
};
