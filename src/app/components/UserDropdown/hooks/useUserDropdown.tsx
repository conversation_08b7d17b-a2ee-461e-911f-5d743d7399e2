import { useMemo } from 'react';
import { useRouter } from 'next/router';
import { signOut, useSession } from 'next-auth/react';
import { useTranslate } from '@tolgee/react';

import { useAbility } from '~/app/contexts/AbilityContext';
import { useGetProfilePicture } from '~/entities/Employee/hooks/useGetProfilePicture';
import { SignOut } from '~/shared/components/icons/SignOut';

type UseUserDropdownProps = {
  user: any;
};

export const useUserDropdown = ({ user }: UseUserDropdownProps) => {
  const { t } = useTranslate();
  const nextAuthSession = useSession();
  const router = useRouter();
  const ability = useAbility();
  const basePath = process.env.NEXT_PUBLIC_BASE_PATH;

  async function handleSignOut() {
    await signOut();
  }

  const items = useMemo(() => {
    const dropdownItems = [
      {
        label: t('common_logout'),
        onSelect: handleSignOut,
        icon: <SignOut />,
      },
    ];

    return dropdownItems;
  }, [t]);

  const { data } = useGetProfilePicture(user.globalId ?? '');
  const imageSrc = data ? URL.createObjectURL(data) : undefined;

  const profileInfo = {
    name: user.name ?? '',
    urlImage: imageSrc,
    zone: user.zone ?? '',
  };

  return {
    items,
    profileInfo,
  };
};
