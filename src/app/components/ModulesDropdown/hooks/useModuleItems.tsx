import { ModuleDropdownProps } from '@ghq-abi/design-system';
import { useTranslate } from '@tolgee/react';

import { Icon } from '~/shared/components';
import { OtherAppsIcon } from '~/shared/components/icons';

const style = {
  color: '#191F2E',
  size: 16,
};

export function useModuleItems() {
  const { t } = useTranslate();

  const items: ModuleDropdownProps['items'] = [
    {
      label: t('common_my_cheershub'),
      subtitle: t('common_my_cheershub'),
      icon: <Icon.Cheershub color={style.color} size={style.size} />,
      url: process.env.NEXT_PUBLIC_CHEERSHUB_URL,
    },
    {
      label: t('common_my_180_360'),
      subtitle: t('common_lcm'),
      icon: <Icon.Target color={style.color} size={style.size} />,
      url: process.env.NEXT_PUBLIC_LCM_URL,
    },
    {
      label: t('common_my_bonus'),
      subtitle: t('common_compensations'),
      icon: <Icon.MoneyBag color={style.color} size={style.size} />,
      url: process.env.NEXT_PUBLIC_MY_COMPENSATION_URL,
    },
    {
      label: t('common_my_talent_card'),
      subtitle: t('common_opr'),
      icon: <Icon.Badge color={style.color} size={style.size} />,
      url: process.env.NEXT_PUBLIC_MY_TALENT_CARD_URL,
    },
    {
      label: 'Workday',
      subtitle: 'Workday',
      icon: <OtherAppsIcon />,
      url: process.env.NEXT_PUBLIC_WORKDAY_URL,
    },
  ];

  return { items };
}
