import { globalCss } from '@ghq-abi/design-system';

const basePath = process.env.NEXT_PUBLIC_BASE_PATH;

export const globalStyles = globalCss({
  // @ts-ignore

  '@font-face': [
    {
      fontFamily: 'OpenSans',
      src: `url('${basePath}/fonts/OpenSans-Light.woff2') format('woff2'),
      url('/fonts/OpenSans-Light.woff') format('woff')`,
      fontWeight: 300,
      fontDisplay: 'swap',
      fontStyle: 'normal',
    },
    {
      fontFamily: 'OpenSans',
      src: `url('${basePath}/fonts/OpenSans-Regular.woff2') format('woff2'),
      url('/fonts/OpenSans-Regular.woff') format('woff')`,
      fontWeight: 400,
      fontDisplay: 'swap',
      fontStyle: 'normal',
    },
    {
      fontFamily: 'OpenSans',
      src: `url('${basePath}/fonts/OpenSans-SemiBold.woff2') format('woff2'),
      url('/fonts/OpenSans-SemiBold.woff') format('woff')`,
      fontWeight: 600,
      fontDisplay: 'swap',
      fontStyle: 'normal',
    },
    {
      fontFamily: 'OpenSans',
      src: `url('${basePath}/fonts/OpenSans-Bold.woff2') format('woff2'),
      url('/fonts/OpenSans-Bold.woff') format('woff')`,
      fontWeight: 700,
      fontDisplay: 'swap',
      fontStyle: 'normal',
    },
    {
      fontFamily: 'OpenSans',
      src: `url('${basePath}/fonts/OpenSans-ExtraBold.woff2') format('woff2'),
      url('/fonts/OpenSans-ExtraBold.woff') format('woff')`,
      fontWeight: 800,
      fontDisplay: 'swap',
      fontStyle: 'normal',
    },
  ],
  // Print options
  '@media print': {
    // Remove page margins and set paper size and orientation
    '@page': {
      size: 'A4 landscape',
      margin: 0,
    },

    // Reset html and body to fill the full printable area
    'html, body': {
      width: '100%',
      height: '100%',
      minHeight: '100%',
      margin: 0,
      padding: 0,
      overflow: 'hidden', // Prevent scrollbars,
      display: 'block', // Ensure proper flow layout
    },

    // Hide everything by default
    'body *': {
      visibility: 'hidden', // Ensure hidden by default for compatibility
    },

    // Show elements with the `print` class
    '.print': {
      visibility: 'visible', // Make it visible
      position: 'absolute', // Stretch it to the page
      top: 0,
      left: 0,
      width: '100%',
      height: '100vh', // Full viewport height for Chrome
      minHeight: '100vh', // Ensure it stretches to the viewport height
      margin: 0,
      padding: 0,
      display: 'block',
      boxSizing: 'border-box',
      '-webkit-print-color-adjust': 'exact', // Force color rendering in WebKit browsers
      'print-color-adjust': 'exact', // Standard property for color adjustment
      color: 'inherit', // Ensure text colors are preserved
      backgroundColor: 'inherit', // Ensure background colors are preserved
    },

    // Ensure all child elements of `.print` are visible
    '.print *': {
      visibility: 'visible',
      '-webkit-print-color-adjust': 'exact',
      'print-color-adjust': 'exact',
      color: 'inherit',
      backgroundColor: 'inherit',
    },

    '.no-print *': {
      visibility: 'hidden',
    },
  },
  '.no-display': {
    display: 'none',
  },

  '*, *::before, *::after': {
    margin: 0,
    padding: 0,
    'box-sizing': 'border-box',

    '-webkit-font-smoothing': 'antialiased',
    '-moz-osx-font-smoothing': 'grayscale',
  },

  'html, body, #__next': {
    minHeight: '100vh',
  },

  body: {
    backgroundColor: '$gray100',

    '::-webkit-scrollbar': {
      width: '6px !important',
      height: '6px !important',
    },
    '::-webkit-scrollbar-track': {
      borderRadius: '4px !important',
      background: '#f1f1f1 !important',
    },
    '::-webkit-scrollbar-thumb': {
      backgroundColor: '#7c7c7c !important',
      borderRadius: '20px !important',
      border: '3px solid #7c7c7c !important',
    },
    scrollbarColor: 'unset !important',
  },

  '*': {
    fontFamily: '$openSans',
  },

  a: {
    textDecoration: 'none',
  },
});
