import { DefaultSession } from 'next-auth';
import { DefaultJWT } from 'next-auth/jwt';

import { AuthenticatedUser } from './shared/auth/types/next-auth';

declare global {
  interface Window {
    DD_RUM: Record<string, unknown>;
    clarity?: (command: string, ...args: unknown[]) => void;
  }
}
declare module 'next-auth' {
  interface User extends CommonProfile {
    email?: string;
  }

  interface Profile {
    ctry: string;
    email: string;
    name: string;
    upn: string;
  }

  interface Session {
    unlinked?: boolean;
    user: AuthenticatedUser;
  }
}

declare module 'next-auth/jwt' {
  interface JWT extends DefaultJWT {
    user?: AuthenticatedUser | null;
  }
}
