import type {
  GetServerSidePropsContext,
  NextApiRequest,
  NextApiResponse,
} from 'next';
import {
  AuthConfig,
  authServerClient,
  getServerSession,
} from '@ghq-abi/auth-client-lib/server';
import { uniq } from 'lodash';

import { loginSSO } from '~/shared/services/auth';

async function abilitiesCallback(token?: string) {
  if (token) {
    const response = await loginSSO(token);

    return {
      roles: response.data.results[0]?.roles_json ?? [],
      permissions: uniq(response.data.results[0]?.permissions_json ?? []),
    };
  }

  return;
}

const authConfig = new AuthConfig(abilitiesCallback);

export default async function auth(req: NextApiRequest, res: NextApiResponse) {
  return authServerClient(req, res, authConfig);
}

export async function getServerSessionWrapper(
  req: GetServerSidePropsContext['req'],
  res: GetServerSidePropsContext['res'],
) {
  return getServerSession(req, res, authConfig);
}
