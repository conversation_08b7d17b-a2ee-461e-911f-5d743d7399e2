import { getServerSessionWrapper } from './api/auth/[...nextauth]';

export default function Index() {
  return null;
}

export const getServerSideProps = async (context: { req: any; res: any }) => {
  const { req, res } = context;
  const session = await getServerSessionWrapper(req, res);

  // if (
  //   !session ||
  //   session?.error ||
  //   !session?.user.token ||
  //   !session?.user.refreshToken
  // ) {
  //   return {
  //     redirect: {
  //       permanent: false,
  //       destination: '/auth/redirect',
  //     },
  //   };
  // }

  return {
    props: {
      session,
    },
    redirect: {
      destination: '/proposals',
      permanent: false,
    },
  };
};
