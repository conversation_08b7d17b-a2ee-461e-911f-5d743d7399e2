import NextDocument, { Head, Html, Main, NextScript } from 'next/document';
import { getCssText, reusables } from '@ghq-abi/design-system';

import { DEFAULT_LANGUAGE } from '~/shared/constants/i18n';

export default class Document extends NextDocument {
  render() {
    const basePath = process.env.NEXT_PUBLIC_BASE_PATH;

    return (
      <Html
        lang={
          this.props.__NEXT_DATA__.props.pageProps.language ?? DEFAULT_LANGUAGE
        }
        translate="no"
      >
        <Head>
          <link
            rel="preload"
            href={`${basePath}/fonts/OpenSans-ExtraBold.woff`}
            as="font"
            type="font/woff"
            crossOrigin="anonymous"
          />
          <link
            rel="preload"
            href={`${basePath}/fonts/OpenSans-ExtraBold.woff2`}
            as="font"
            type="font/woff2"
            crossOrigin="anonymous"
          />
          <link
            rel="preload"
            href={`${basePath}/fonts/OpenSans-Bold.woff`}
            as="font"
            type="font/woff"
            crossOrigin="anonymous"
          />
          <link
            rel="preload"
            href={`${basePath}/fonts/OpenSans-Bold.woff2`}
            as="font"
            type="font/woff2"
            crossOrigin="anonymous"
          />
          <link
            rel="preload"
            href={`${basePath}/fonts/OpenSans-Light.woff`}
            as="font"
            type="font/woff"
            crossOrigin="anonymous"
          />
          <link
            rel="preload"
            href={`${basePath}/fonts/OpenSans-Light.woff2`}
            as="font"
            type="font/woff2"
            crossOrigin="anonymous"
          />
          <link
            rel="preload"
            href={`${basePath}/fonts/OpenSans-SemiBold.woff`}
            as="font"
            type="font/woff"
            crossOrigin="anonymous"
          />
          <link
            rel="preload"
            href={`${basePath}/fonts/OpenSans-SemiBold.woff2`}
            as="font"
            type="font/woff2"
            crossOrigin="anonymous"
          />
          <link
            rel="preload"
            href={`${basePath}/fonts/OpenSans-Regular.woff`}
            as="font"
            type="font/woff"
            crossOrigin="anonymous"
          />
          <link
            rel="preload"
            href={`${basePath}/fonts/OpenSans-Regular.woff2`}
            as="font"
            type="font/woff2"
            crossOrigin="anonymous"
          />
          <link
            rel="apple-touch-icon"
            sizes="180x180"
            href={`${basePath}/img/apple-touch-icon.png`}
          />
          <link
            rel="icon"
            type="image/png"
            sizes="32x32"
            href={`${basePath}/img/favicon-32x32.png`}
          />
          <link
            rel="icon"
            type="image/png"
            sizes="16x16"
            href={`${basePath}/img/favicon-16x16.png`}
          />
          <link rel="manifest" href={`${basePath}/assets/site.webmanifest`} />

          <style
            id="stitches"
            // TODO: Check if this bit is vulnerable for XSS
            // eslint-disable-next-line react/no-danger
            dangerouslySetInnerHTML={{ __html: getCssText() }}
          />
        </Head>
        <body className={reusables.scrollbar()}>
          <Main />
          <NextScript />
        </body>
      </Html>
    );
  }
}
