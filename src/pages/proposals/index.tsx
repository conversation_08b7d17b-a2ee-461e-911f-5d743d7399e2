import { GetStaticProps } from 'next';

import * as Home from '~/entities/Home';
import { DeliverableType } from '~/entities/Home/components/Catalog/types';
import deliverablesService from '~/shared/services/deliverables';
import proposalService from '~/shared/services/proposal';
import { DeliverableItemsResponse } from '~/shared/types/DeliverablesService';
import { ProposalItemsResponse } from '~/shared/types/ProposalService';

interface IndexProps {
  initialKpis: DeliverableItemsResponse;
  initialProposals: ProposalItemsResponse;
}

export default function Index({ initialKpis, initialProposals }: IndexProps) {
  return (
    <Home.Page initialKpis={initialKpis} initialProposals={initialProposals} />
  );
}

export const getStaticProps: GetStaticProps<IndexProps> = async () => {
  try {
    const initialKpis = await deliverablesService.getDeliverables({
      pageNumber: 1,
      pageSize: 100,
      deliverableTypes: [DeliverableType.KPI, DeliverableType.PROJECT],
    });

    const initialProposals = await proposalService.getProposals({
      pageNumber: 1,
      pageSize: 9,
    });

    return {
      props: {
        initialKpis,
        initialProposals,
      },
    };
  } catch (error) {
    console.error('Error fetching initial data:', error);

    return {
      props: {
        initialKpis: {
          data: [],
          pageNumber: 1,
          pageSize: 100,
          totalRecords: 0,
        },
        initialProposals: {
          data: [],
          pageNumber: 1,
          pageSize: 9,
          totalRecords: 0,
        },
      },
    };
  }
};
