import Link from 'next/link';
import { Text } from '@ghq-abi/design-system';

import { withSSRUnauth } from '~/app/hocs';
import { AuthErrorTemplate } from '~/app/templates/AuthError';

export default function AuthError() {
  return (
    <AuthErrorTemplate>
      <Text css={{ lineHeight: '$shorter', fontSize: '$2' }}>
        You currently do not have access to this application. Please reach out
        to your People Business Partner if you think this is an error or{' '}
        <Text
          css={{
            fontWeight: '$bold',
            textDecoration: 'underline',
            display: 'inline',
          }}
        >
          <Link href="/catchball/auth/redirect">sign-in again.</Link>
        </Text>
      </Text>
    </AuthErrorTemplate>
  );
}

export const getServerSideProps = withSSRUnauth();

AuthError.unauth = true;
