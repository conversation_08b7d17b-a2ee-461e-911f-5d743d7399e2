import { useMemo } from 'react';
import { useQuery } from '@tanstack/react-query';

import { peoplePlatformPhotoApi } from '~/shared/services/api';

async function getProfilePicture(globalId?: string) {
  try {
    const { data } = await peoplePlatformPhotoApi().get<Blob>(
      `/thumb/${globalId}`,
    );

    return data;
  } catch {
    return null;
  }
}

export function useGetProfilePicture(globalId: string) {
  const query = useQuery({
    queryKey: ['getProfilePicture', globalId],
    queryFn: () => getProfilePicture(globalId),
    staleTime: Infinity,
    cacheTime: Infinity,
    retry: false,
    retryOnMount: false,
    refetchOnWindowFocus: false,
    refetchOnMount: false,
  });

  const imageSrc = useMemo(
    () => (query?.data ? URL.createObjectURL(query.data) : null),
    [query.data],
  );
  return Object.assign(query, {
    imageSrc,
  });
}
