import { Target } from '~/shared/types/Target';
import { TargetTypeEnum } from '~/shared/utils/enums/target-type';

export function filterTargetsByType(
  targetsList: Target[],
  targetType: TargetTypeEnum,
): Target[] {
  return targetsList
    .filter(target =>
      target.targetTypes?.some(
        targetTypeItem => targetTypeItem.type === targetType,
      ),
    )
    .map(target => ({
      ...target,
      children:
        target.children?.filter(child =>
          child.targetTypes?.some(
            targetTypeItem => targetTypeItem.type === targetType,
          ),
        ) || [],
    }));
}
