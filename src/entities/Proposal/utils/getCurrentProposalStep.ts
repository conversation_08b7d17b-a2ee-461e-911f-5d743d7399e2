import { ProposalStatusEnum } from '~/shared/utils/enums';

import { PROPOSAL_STEPS } from '../constants/steps';

export function getCurrentProposalStep(
  proposalStatus: ProposalStatusEnum | undefined,
) {
  switch (proposalStatus) {
    case ProposalStatusEnum.IN_PROGRESS_FEEDBACK:
      return PROPOSAL_STEPS.FEEDBACK;
    case ProposalStatusEnum.IN_PROGRESS_FINAL:
      return PROPOSAL_STEPS.FINAL;
    case ProposalStatusEnum.COMPLETED:
      return PROPOSAL_STEPS.COMPARATIVE;
    default:
      return PROPOSAL_STEPS.PROPOSAL;
  }
}
