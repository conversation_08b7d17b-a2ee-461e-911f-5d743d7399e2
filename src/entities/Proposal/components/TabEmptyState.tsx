import React from 'react'
import { Container, Typography } from '@ghq-abi/design-system-v2';
import { Inbox } from 'react-bootstrap-icons';

interface TabEmptyStateProps {
  title: string;
  description: string;
}

export function TabEmptyState({
  title,
  description
}: TabEmptyStateProps) {
  return (
    <Container className='flex flex-col items-center justify-center p-4 h-full'>
      <Container className='bg-[#F5F6F7] p-4 flex flex-col items-center justify-center rounded-lg w-[328px]'>
        <Container className='bg-[#191F2E14] rounded-full w-[64px] h-[64px] flex items-center justify-center'>
          <Inbox className='w-[24px] h-[24px]' />
        </Container>
        <Typography variant='body-md-bold' className='mt-4 text-[#191F2E]'>{title}</Typography>
        <Typography variant='body-sm-regular' className='text-center text-[#7D8597]'>{description}</Typography>
      </Container>
    </Container>
  )
}
