import React, { useMemo, useState } from 'react';
import { Trash } from 'react-bootstrap-icons';
import {
  DndContext,
  DragEndEvent,
  DragOverlay,
  DragStartEvent,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import {
  Button,
  FooterActionBar,
  Skeleton,
  Tooltip,
  Typography,
} from '@ghq-abi/design-system-v2';
import { useMutation } from '@tanstack/react-query';
import { useTranslate } from '@tolgee/react';

import { CatalogCard } from '~/entities/Home/components';
import { useCatalog } from '~/entities/Home/hooks/useCatalog';
import { useCatalogFilter } from '~/entities/Home/hooks/useCatalogFilter';
import { ChildCard, TargetCard } from '~/shared/components';
import { ActionModal } from '~/shared/components/ActionModal';
import { useActionModal } from '~/shared/components/ActionModal/useActionModal';
import proposalService from '~/shared/services/proposal';
import { DeliverableItem } from '~/shared/types/Deliverable';
import { Proposal } from '~/shared/types/Proposal';
import { Target } from '~/shared/types/Target';
import { ProposalStatusEnum } from '~/shared/utils/enums';
import { TargetTypeEnum } from '~/shared/utils/enums/target-type';

import { CreateEditTargetBody } from '../../types';
import { filterTargetsByType } from '../../utils/targetFilters';
import { CatalogListProposal } from '../CatalogListProposal';

import { CreateTargetDrawer } from './CreateTargetDrawer';
import { DroppableTarget } from './DroppableTarget';
import { TargetsList } from './TargetsList';

interface ProposalDragProps {
  proposalStatus?: ProposalStatusEnum;
  proposalUid: string;
  targets: Target[];
  onProposalUpdate?: (updatedProposal: Proposal) => void;
  isLoading?: boolean;
}

export function ProposalDrag({
  proposalStatus,
  proposalUid,
  targets,
  onProposalUpdate,
  isLoading: isLoadingTargets,
}: ProposalDragProps) {
  const { t } = useTranslate();
  const actionModal = useActionModal();
  const [draggedItem, setDraggedItem] = useState<
    DeliverableItem | Target | null
  >(null);
  const [selectedTargets, setSelectedTargets] = useState<Target[]>(
    filterTargetsByType(targets || [], TargetTypeEnum.PROPOSAL),
  );
  const [isDraggingNewItem, setIsDraggingNewItem] = useState(false);
  const [isOpenDrawer, setIsOpenDrawer] = useState<boolean>(false);
  const [drawerDeliverable, setDrawerDeliverable] = useState<
    DeliverableItem | Target
  >();
  const [isEditDrawer, setIsEditDrawer] = useState<boolean>(false);

  const totalWeight = useMemo(
    () =>
      selectedTargets.reduce((sum, target) => sum + (target.weight || 0), 0),
    [selectedTargets],
  );

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 3,
      },
    }),
  );

  const { isLoading, mutate: mutateFeedback } = useMutation({
    mutationFn: () => {
      return proposalService.changeProposalStatus(
        proposalUid,
        ProposalStatusEnum.IN_PROGRESS_FEEDBACK,
      );
    },
    onSuccess: (response: Proposal) => {
      onProposalUpdate?.(response);
    },
    onError: error => {
      console.error(error);
    },
  });

  const { isLoading: isLoadingDelete, mutate: mutateDelete } = useMutation({
    mutationFn: (targets: Target[]) => {
      const targetsToDelete = targets.map(target => {
        return { uid: target.uid || '', targetType: TargetTypeEnum.PROPOSAL };
      });
      return proposalService.deleteTargets(proposalUid, targetsToDelete);
    },
    onSuccess: (proposal: Proposal) => {
      setSelectedTargets(proposal.targets || []);
      actionModal.closeModal();
    },
    onError: error => {
      console.error(error);
    },
  });

  const getChildForBodyRequest = (child: Target[]) => {
    return child.map(c => ({
      weight: c.weight,
      scope: c.scope,
      uidDeliverable: c.deliverable?.uid || '',
      ...(c.uid && { uid: c.uid }),
    }));
  };

  const { mutate: mutateMergeTargets } = useMutation({
    mutationFn: (values: { firstTarget: Target; secondTarget: Target }) => {
      const firstTarget = values.firstTarget as Target;
      const secondTarget = values.secondTarget as Target;

      const treatedChildren = [];

      if (firstTarget.children && firstTarget.children.length > 0) {
        treatedChildren.push(...getChildForBodyRequest(firstTarget.children));
      } else {
        treatedChildren.push({
          weight: firstTarget.weight,
          scope: firstTarget.scope,
          uidDeliverable:
            firstTarget.uidDeliverable || firstTarget.deliverable?.uid || '',
          ...(firstTarget.uid && { uid: firstTarget.uid }),
        });
      }

      if (secondTarget.children && secondTarget.children.length > 0) {
        treatedChildren.push(...getChildForBodyRequest(secondTarget.children));
      } else {
        treatedChildren.push({
          weight: secondTarget.weight,
          scope: secondTarget.scope,
          uidDeliverable:
            secondTarget.uidDeliverable || secondTarget.deliverable?.uid || '',
          ...(secondTarget.uid && { uid: secondTarget.uid }),
        });
      }

      const data: CreateEditTargetBody = {
        targets: [
          {
            weight: (firstTarget.weight || 0) + (secondTarget.weight || 0),
            targetType: TargetTypeEnum.PROPOSAL,
            children: treatedChildren,
          },
        ],
      };

      return proposalService.createTarget(proposalUid, data);
    },
    onSuccess: (proposal: Proposal) => {
      setSelectedTargets(proposal.targets || []);
    },
    onError: error => {
      console.error(error);
    },
  });

  const catalogFilterHook = useCatalogFilter();
  const { filters } = catalogFilterHook;

  const {
    data: deliverables,
    isSearchLoading,
    isInitialLoading,
    isError,
  } = useCatalog(1, 100, undefined, filters);

  const availableDeliverables = useMemo(() => {
    const selectedUids = new Set(
      selectedTargets.flatMap(d => {
        if (d.children && d.children.length > 0) {
          return d.children
            .map(child => child.deliverable?.uid)
            .filter(Boolean);
        } else {
          return d.deliverable?.uid ? [d.deliverable.uid] : [];
        }
      }),
    );

    const deliverableItems = deliverables?.data || [];
    return deliverableItems.filter(d => !selectedUids.has(d.uid));
  }, [deliverables, selectedTargets]);

  const handleDragStart = (event: DragStartEvent) => {
    const item = event.active.data.current?.data;

    if (item) {
      setDraggedItem(item);
      const isFromSideList = availableDeliverables.some(
        d => d.uid === item.uid,
      );
      setIsDraggingNewItem(isFromSideList);
    }
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (over && over.id === 'selection-area') {
      const deliverable = active.data.current?.data;
      if (
        deliverable &&
        !selectedTargets.find(d => d.uid === deliverable.uid)
      ) {
        // TODO: improve this using a hook or context?
        setIsEditDrawer(false);
        setDrawerDeliverable(deliverable);
        setIsOpenDrawer(true);
      }
    } else if (over && over.id.toString().includes('target')) {
      const firstTarget = over.data.current?.target as Target;
      const secondTarget = active.data.current?.data as Target;

      if (firstTarget && secondTarget && firstTarget.uid !== secondTarget.uid) {
        actionModal.openModal({
          title: t('common_merge_targets'),
          message: t('common_do_you_really_want_to_merge_the_targets'),
          actions: [
            {
              label: t('common_yes'),
              onClick: () => {},
            },
            {
              label: t('common_no'),
              onClick: actionModal.closeModal,
              variant: 'secondary',
            },
          ],
          onConfirm: async () => {
            const values = { firstTarget, secondTarget };
            await mutateMergeTargets(values);
            actionModal.closeModal();
          },
        });

        // addParentTarget(firstTarget, secondTarget);
      }
    }

    setDraggedItem(null);
    setIsDraggingNewItem(false);
  };

  const onDrawerSuccessSubmit = (proposal: Proposal) => {
    setSelectedTargets(proposal.targets || []);
  };

  const handleSubmit = () => {
    mutateFeedback();
  };

  const handleOpenEditDrawer = (target: Target) => {
    setIsEditDrawer(true);
    setDrawerDeliverable(target);
    setIsOpenDrawer(true);
  };

  const handleTargetRemove = (targets: Target[]) => {
    actionModal.openModal({
      title:
        targets.length > 1 ? t('common_clear_list') : t('common_delete_target'),
      message:
        targets.length > 1
          ? t('common_do_you_really_want_to_clear_the_list')
          : t('common_do_you_really_want_to_delete_this_target'),
      actions: [
        {
          label: t('common_yes'),
          onClick: () => {},
        },
        {
          label: t('common_no'),
          onClick: actionModal.closeModal,
          variant: 'secondary',
        },
      ],
      onConfirm: async () => {
        if (isLoadingDelete) {
          return;
        }
        await mutateDelete(targets);
      },
    });
  };

  return (
    <>
      <DndContext
        sensors={sensors}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
      >
        <div className="flex h-[calc(100vh-340px)] gap-6">
          <CatalogListProposal
            deliverables={availableDeliverables}
            title={t('common_catalog')}
            isInitialLoading={isInitialLoading}
            isSearchLoading={isSearchLoading}
            isError={isError}
            catalogFilterHook={catalogFilterHook}
          />
          <TargetsList
            selectedData={selectedTargets}
            isDraggingNewItem={isDraggingNewItem}
            actions={[
              {
                label: t('common_clear_list'),
                onClick: () => handleTargetRemove(selectedTargets),
                variant: 'secondary',
                iconLeft: <Trash />,
                disabled: selectedTargets.length === 0,
              },
            ]}
          >
            {isLoadingTargets ? (
              Array.from({ length: 3 }).map((_, index) => (
                <Skeleton key={index} className="w-full h-[168px]" />
              ))
            ) : (
              <>
                {selectedTargets.map(target => {
                  if (target.children && target.children.length > 1) {
                    return (
                      <DroppableTarget
                        isDragging={isDraggingNewItem}
                        key={target.uid}
                        target={target}
                      >
                        <TargetCard
                          key={target.uid}
                          data={target}
                          proposalStatus={proposalStatus}
                          hideChildren={target.children.length <= 1}
                          currentTargetType={TargetTypeEnum.PROPOSAL}
                          onEditTarget={handleOpenEditDrawer}
                          onRemoveActionClick={handleTargetRemove}
                          hasManagerPermission
                          isOnDroppableArea
                        />
                      </DroppableTarget>
                    );
                  }
                  return (
                    <DroppableTarget
                      isDragging={isDraggingNewItem}
                      key={target.uid}
                      target={target}
                    >
                      <ChildCard
                        key={target.uid}
                        target={target}
                        disableDrag={false}
                        onEditTarget={handleOpenEditDrawer}
                        onRemoveActionClick={handleTargetRemove}
                        showActions
                      />
                    </DroppableTarget>
                  );
                })}
              </>
            )}
          </TargetsList>
        </div>
        <DragOverlay>
          {(draggedItem as Target)?.deliverable ? (
            <ChildCard target={draggedItem as Target} />
          ) : (
            <CatalogCard data={draggedItem as DeliverableItem} isDragging />
          )}
        </DragOverlay>
      </DndContext>
      {isOpenDrawer && (
        <CreateTargetDrawer
          isOpen={isOpenDrawer}
          onClose={() => setIsOpenDrawer(false)}
          isEdit={isEditDrawer}
          data={drawerDeliverable}
          proposalId={proposalUid}
          onSuccessSubmit={onDrawerSuccessSubmit}
        />
      )}
      <ActionModal
        isOpen={actionModal.isOpen}
        openModal={actionModal.openModal}
        closeModal={actionModal.closeModal}
        title={actionModal.title}
        message={actionModal.message}
        actions={[
          {
            label: t('common_yes'),
            onClick: actionModal.handleConfirm,
            variant: 'primary',
          },
          {
            label: t('common_no'),
            onClick: actionModal.closeModal,
            variant: 'secondary',
          },
        ]}
      />
      <FooterActionBar>
        <Button
          variant="secondary"
          border="default"
          className="w-fit"
          onClick={() => window.history.back()}
        >
          Back
        </Button>

        <Tooltip.Provider delayDuration={0}>
          <Tooltip.Root>
            <Tooltip.Trigger>
              <Button
                id="deliverable-form"
                isLoading={isLoading}
                variant="primary"
                className="w-fit"
                round="md"
                onClick={handleSubmit}
                disabled={totalWeight !== 100}
              >
                {t('common_submit')}
              </Button>
            </Tooltip.Trigger>
            <Tooltip.Content
              className={`z-[9999] break-words max-w-[90vw] ${
                totalWeight === 100 ? 'hidden' : ''
              }`}
              side="bottom"
              align="center"
            >
              <Typography variant="metadata-sm-regular">
                {`${t('common_total_weight_minimal_value')} ${totalWeight}%`}
              </Typography>
            </Tooltip.Content>
          </Tooltip.Root>
        </Tooltip.Provider>
      </FooterActionBar>
    </>
  );
}
