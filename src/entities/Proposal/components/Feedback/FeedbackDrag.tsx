import React from 'react';
import { Trash } from 'react-bootstrap-icons';
import {
  DndContext,
  DragOverlay,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import {
  Button,
  FooterActionBar,
  Tooltip,
  Typography,
} from '@ghq-abi/design-system-v2';
import { useTranslate } from '@tolgee/react';

import { CatalogCard } from '~/entities/Home/components';
import { useCatalogFilter } from '~/entities/Home/hooks/useCatalogFilter';
import { ChildCard, TargetCard } from '~/shared/components';
import { ActionModal } from '~/shared/components/ActionModal';
import { DeliverableItem } from '~/shared/types/Deliverable';
import { Target } from '~/shared/types/Target';
import { TargetTypeEnum } from '~/shared/utils/enums/target-type';

import { CreateTargetDrawer } from '../Proposal/CreateTargetDrawer';
import { DroppableTarget } from '../Proposal/DroppableTarget';
import { TargetsList } from '../Proposal/TargetsList';

import { CatalogWithTabs } from './CatalogWithTabs';
import { FeedbackDragProps } from './types';
import { useFeedback } from './useFeedback';

export function FeedbackDrag({
  proposalStatus,
  proposalUid,
  targets,
  allTargets = [],
  onProposalUpdate,
}: FeedbackDragProps) {
  const { t } = useTranslate();
  const catalogFilterHook = useCatalogFilter();

  const {
    selectedTargets,
    acceptedTargetUids,
    draggedItem,
    isDraggingNewItem,
    isOpenDrawer,
    drawerDeliverable,
    isLoading,
    isLoadingDelete,
    isLoadingMergeTargets,
    isLoadingAcceptAllTargets,
    handleAcceptTarget,
    handleDragStart,
    handleDragEnd,
    handleSubmit,
    handleClearDeliverable,
    handleRemoveTargets,
    handleFillWithProposal,
    onDrawerSuccessSubmit,
    setIsOpenDrawer,
    availableDeliverables,
    selectedFeedbackTargetUids,
    isTargetLoading,
    actionModal,
    totalWeight,
    isEditDrawer,
    handleOpenEditDrawer,
  } = useFeedback({
    proposalUid,
    targets,
    allTargets,
    catalogFilterHook,
    onProposalUpdate,
  });

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 3,
      },
    }),
  );

  return (
    <>
      <DndContext
        sensors={sensors}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
      >
        <div className="flex h-[calc(100vh-340px)] gap-6">
          <CatalogWithTabs
            deliverables={availableDeliverables}
            proposalTargets={allTargets}
            isInitialLoading={false}
            isSearchLoading={false}
            isError={false}
            catalogFilterHook={catalogFilterHook}
            onAcceptTarget={handleAcceptTarget}
            acceptedTargetUids={[
              ...acceptedTargetUids,
              ...Array.from(selectedFeedbackTargetUids),
            ]}
            isTargetLoading={isTargetLoading}
            filterAcceptedFromFeedbackTab={true}
          />
          <TargetsList
            selectedData={selectedTargets}
            isDraggingNewItem={isDraggingNewItem}
            actions={[
              {
                label: t('common_clear_list'),
                onClick: handleClearDeliverable,
                variant: 'secondary',
                iconLeft: <Trash />,
                disabled: selectedTargets.length === 0,
              },
              {
                label: t('common_fill_list_with_proposal'),
                onClick: handleFillWithProposal,
                variant: 'secondary',
                disabled: isLoadingAcceptAllTargets || isLoadingDelete,
              },
            ]}
          >
            {selectedTargets.map(target => {
              if (target.children && target.children.length > 1) {
                return (
                  <DroppableTarget
                    isDragging={isDraggingNewItem}
                    key={target.uid}
                    target={target}
                  >
                    <TargetCard
                      key={target.uid}
                      data={target}
                      proposalStatus={proposalStatus}
                      hideChildren={target.children.length <= 1}
                      currentTargetType={TargetTypeEnum.FEEDBACK}
                      onRemoveActionClick={handleRemoveTargets}
                      onEditTarget={handleOpenEditDrawer}
                      isOnDroppableArea
                    />
                  </DroppableTarget>
                );
              }
              return (
                <DroppableTarget
                  isDragging={isDraggingNewItem}
                  key={target.uid}
                  target={target}
                >
                  <ChildCard
                    key={target.uid}
                    target={target}
                    disableDrag={false}
                    onRemoveActionClick={handleRemoveTargets}
                    onEditTarget={handleOpenEditDrawer}
                    showActions
                  />
                </DroppableTarget>
              );
            })}
          </TargetsList>
        </div>
        <DragOverlay>
          {(draggedItem as Target)?.deliverable ? (
            <ChildCard target={draggedItem as Target} />
          ) : (
            <CatalogCard data={draggedItem as DeliverableItem} isDragging />
          )}
        </DragOverlay>
      </DndContext>
      <CreateTargetDrawer
        isOpen={isOpenDrawer}
        onClose={() => setIsOpenDrawer(false)}
        data={drawerDeliverable}
        isEdit={isEditDrawer}
        proposalId={proposalUid}
        onSuccessSubmit={onDrawerSuccessSubmit}
        targetType={TargetTypeEnum.FEEDBACK}
      />
      <ActionModal
        isOpen={actionModal.isOpen}
        openModal={actionModal.openModal}
        closeModal={actionModal.closeModal}
        title={actionModal.title}
        message={actionModal.message}
        actions={[
          {
            label: t('common_yes'),
            onClick: actionModal.handleConfirm,
            variant: 'primary',
            isLoading: isLoadingMergeTargets,
          },
          {
            label: t('common_no'),
            onClick: actionModal.closeModal,
            variant: 'secondary',
          },
        ]}
      />
      <FooterActionBar>
        <Button
          variant="secondary"
          border="default"
          className="w-fit"
          onClick={() => window.history.back()}
        >
          Back
        </Button>

        <Tooltip.Provider delayDuration={0}>
          <Tooltip.Root>
            <Tooltip.Trigger>
              <Button
                id="deliverable-form"
                isLoading={isLoading}
                variant="primary"
                className="w-fit"
                round="md"
                onClick={handleSubmit}
                disabled={totalWeight !== 100}
              >
                {t('common_submit')}
              </Button>
            </Tooltip.Trigger>
            <Tooltip.Content
              className={`z-[9999] break-words max-w-[90vw] ${totalWeight === 100 ? 'hidden' : ''}`}
              side="bottom"
              align="center"
            >
              <Typography variant="metadata-sm-regular">
                {`${t('common_total_weight_minimal_value')} ${totalWeight}%`}
              </Typography>
            </Tooltip.Content>
          </Tooltip.Root>
        </Tooltip.Provider>
      </FooterActionBar>
    </>
  );
}
