import { useMemo, useState } from 'react';
import { useMutation } from '@tanstack/react-query';
import { useTranslate } from '@tolgee/react';

import { useCatalog } from '~/entities/Home/hooks/useCatalog';
import { useActionModal } from '~/shared/components/ActionModal/useActionModal';
import proposalService from '~/shared/services/proposal';
import targetTypesService from '~/shared/services/targetTypes';
import { DeliverableItem } from '~/shared/types/Deliverable';
import { Proposal } from '~/shared/types/Proposal';
import { Target } from '~/shared/types/Target';
import { ProposalStatusEnum } from '~/shared/utils/enums';
import { TargetTypeEnum } from '~/shared/utils/enums/target-type';

import { filterTargetsByType } from '../../utils/targetFilters';

import {
  CreateTargetBody,
  UseFeedbackParams,
  UseFeedbackReturn,
} from './types';

export function useFeedback({
  proposalUid,
  targets,
  allTargets,
  catalogFilterHook,
  onProposalUpdate,
}: UseFeedbackParams): UseFeedbackReturn {
  const { t } = useTranslate();
  const actionModal = useActionModal();

  // States
  const [draggedItem, setDraggedItem] = useState<
    DeliverableItem | Target | null
  >(null);
  const [selectedTargets, setSelectedTargets] = useState<Target[]>(
    filterTargetsByType(targets || [], TargetTypeEnum.FEEDBACK),
  );
  const [acceptedTargetUids, setAcceptedTargetUids] = useState<string[]>([]);
  const [loadingTargetUids, setLoadingTargetUids] = useState<string[]>([]);
  const [isDraggingNewItem, setIsDraggingNewItem] = useState(false);
  const [isOpenDrawer, setIsOpenDrawer] = useState<boolean>(false);
  const [drawerDeliverable, setDrawerDeliverable] = useState<
    DeliverableItem | Target
  >();
  const [isEditDrawer, setIsEditDrawer] = useState<boolean>(false);
  const [agreementChoice, setAgreementChoice] = useState<boolean | null>(null);

  const { filters } = catalogFilterHook;

  const totalWeight = useMemo(
    () =>
      selectedTargets.reduce((sum, target) => sum + (target.weight || 0), 0),
    [selectedTargets],
  );

  const {
    data: deliverables,
    isSearchLoading,
    isInitialLoading,
    isError,
  } = useCatalog(1, 100, undefined, filters);

  // Mutations
  const { isLoading, mutate: mutateFinal } = useMutation({
    mutationFn: () => {
      return proposalService.changeProposalStatus(
        proposalUid,
        ProposalStatusEnum.IN_PROGRESS_FINAL,
      );
    },
    onSuccess: (response: Proposal) => {
      // Proposal sent to final successfully
      onProposalUpdate?.(response);
    },
    onError: error => {
      console.error(error);
    },
  });

  const { isLoading: isLoadingDelete, mutate: mutateDelete } = useMutation({
    mutationFn: (targets: Target[]) => {
      const targetsToDelete = targets.map(target => {
        return { uid: target.uid || '', targetType: TargetTypeEnum.FEEDBACK };
      });

      return proposalService.deleteTargets(proposalUid, targetsToDelete);
    },
    onSuccess: (proposal: Proposal) => {
      const feedbackTargets = filterTargetsByType(
        proposal.targets || [],
        TargetTypeEnum.FEEDBACK,
      );
      setSelectedTargets(feedbackTargets);
      actionModal.closeModal();
    },
    onError: error => {
      console.error(error);
    },
  });

  const getChildForBodyRequest = (child: Target[]) => {
    return child.map(c => ({
      weight: c.weight,
      scope: c.scope,
      uidDeliverable: c.deliverable?.uid || '',
      ...(c.uid && { uid: c.uid }),
    }));
  };

  const { isLoading: isLoadingMergeTargets, mutate: mutateMergeTargets } =
    useMutation({
      mutationFn: (values: { firstTarget: Target; secondTarget: Target }) => {
        const firstTarget = values.firstTarget as Target;
        const secondTarget = values.secondTarget as Target;

        const treatedChildren = [];

        if (firstTarget.children && firstTarget.children.length > 0) {
          treatedChildren.push(...getChildForBodyRequest(firstTarget.children));
        } else {
          treatedChildren.push({
            weight: firstTarget.weight,
            scope: firstTarget.scope,
            uidDeliverable:
              firstTarget.uidDeliverable || firstTarget.deliverable?.uid || '',
            ...(firstTarget.uid && { uid: firstTarget.uid }),
          });
        }

        if (secondTarget.children && secondTarget.children.length > 0) {
          treatedChildren.push(
            ...getChildForBodyRequest(secondTarget.children),
          );
        } else {
          treatedChildren.push({
            weight: secondTarget.weight,
            scope: secondTarget.scope,
            uidDeliverable:
              secondTarget.uidDeliverable ||
              secondTarget.deliverable?.uid ||
              '',
            ...(secondTarget.uid && { uid: secondTarget.uid }),
          });
        }

        const data: CreateTargetBody = {
          targets: [
            {
              weight: (firstTarget.weight || 0) + (secondTarget.weight || 0),
              targetType: TargetTypeEnum.FEEDBACK,
              children: treatedChildren,
            },
          ],
        };

        return proposalService.createTarget(proposalUid, data);
      },
      onSuccess: (proposal: Proposal) => {
        const feedbackTargets = filterTargetsByType(
          proposal.targets || [],
          TargetTypeEnum.FEEDBACK,
        );
        setSelectedTargets(feedbackTargets);
        actionModal.closeModal();
      },
      onError: error => {
        console.error(error);
      },
    });

  const { isLoading: isLoadingAcceptTargets, mutate: mutateAcceptTargets } =
    useMutation({
      mutationFn: (targetUids: string[]) => {
        return targetTypesService.addFeedbackToTargets(
          targetUids,
          agreementChoice ?? undefined,
        );
      },
      onSuccess: (response, targetUids) => {
        const targetUid = targetUids[0];
        const targetToAccept = allTargets.find(
          target => target.uid === targetUid,
        );

        if (targetToAccept) {
          const feedbackTarget: Target = {
            ...targetToAccept,
            targetTypes: [
              ...(targetToAccept.targetTypes || []),
              { type: TargetTypeEnum.FEEDBACK },
            ],
            children:
              targetToAccept.children?.map(child => ({
                ...child,
                targetTypes: child.targetTypes?.some(
                  tt => tt.type === TargetTypeEnum.FEEDBACK,
                )
                  ? child.targetTypes
                  : [
                      ...(child.targetTypes || []),
                      { type: TargetTypeEnum.FEEDBACK },
                    ],
              })) || [],
          };

          setSelectedTargets(prev => {
            const existingTargetIndex = prev.findIndex(
              t => t.uid === targetUid,
            );

            if (existingTargetIndex >= 0) {
              const updatedTargets = [...prev];
              updatedTargets[existingTargetIndex] = feedbackTarget;
              return filterTargetsByType(
                updatedTargets,
                TargetTypeEnum.FEEDBACK,
              );
            } else {
              const updatedTargets = [...prev, feedbackTarget];
              return filterTargetsByType(
                updatedTargets,
                TargetTypeEnum.FEEDBACK,
              );
            }
          });

          setAcceptedTargetUids(prev => [...prev, targetUid]);
        }

        setLoadingTargetUids(prev => prev.filter(uid => uid !== targetUid));
      },
      onError: (error, targetUids) => {
        console.error('Error accepting targets:', error);
        const targetUid = targetUids[0];
        setLoadingTargetUids(prev => prev.filter(uid => uid !== targetUid));
      },
    });

  const {
    isLoading: isLoadingAcceptAllTargets,
    mutate: mutateAcceptAllTargets,
  } = useMutation({
    mutationFn: (targetUids: string[]) => {
      return targetTypesService.addFeedbackToTargets(
        targetUids,
        agreementChoice ?? undefined,
      );
    },
    onSuccess: (response, targetUids) => {
      const targetsToAccept = allTargets.filter(target =>
        targetUids.includes(target.uid || ''),
      );

      const feedbackTargets = targetsToAccept.map(targetToAccept => ({
        ...targetToAccept,
        targetTypes: [
          ...(targetToAccept.targetTypes || []),
          { type: TargetTypeEnum.FEEDBACK },
        ],
        children:
          targetToAccept.children?.map(child => ({
            ...child,
            targetTypes: child.targetTypes?.some(
              tt => tt.type === TargetTypeEnum.FEEDBACK,
            )
              ? child.targetTypes
              : [
                  ...(child.targetTypes || []),
                  { type: TargetTypeEnum.FEEDBACK },
                ],
          })) || [],
      }));

      setSelectedTargets(prev => {
        const updatedTargets = [...prev];

        feedbackTargets.forEach(feedbackTarget => {
          const existingTargetIndex = updatedTargets.findIndex(
            t => t.uid === feedbackTarget.uid,
          );

          if (existingTargetIndex >= 0) {
            updatedTargets[existingTargetIndex] = feedbackTarget;
          } else {
            updatedTargets.push(feedbackTarget);
          }
        });

        return filterTargetsByType(updatedTargets, TargetTypeEnum.FEEDBACK);
      });

      setAcceptedTargetUids(prev => [...prev, ...targetUids]);
      setLoadingTargetUids(prev =>
        prev.filter(uid => !targetUids.includes(uid)),
      );
    },
    onError: (error, targetUids) => {
      console.error('Error accepting all targets:', error);
      setLoadingTargetUids(prev =>
        prev.filter(uid => !targetUids.includes(uid)),
      );
    },
  });

  // Computed values
  const availableDeliverables = useMemo(() => {
    const feedbackTargets = filterTargetsByType(
      selectedTargets,
      TargetTypeEnum.FEEDBACK,
    );

    const selectedUids = new Set(
      feedbackTargets.flatMap(d => {
        if (d.children && d.children.length > 0) {
          return d.children
            .map(child => child.deliverable?.uid)
            .filter(Boolean);
        } else {
          return d.deliverable?.uid ? [d.deliverable.uid] : [];
        }
      }),
    );

    const deliverableItems = deliverables?.data || [];
    return deliverableItems.filter(d => !selectedUids.has(d.uid));
  }, [deliverables, selectedTargets]);

  const selectedFeedbackTargetUids = useMemo(() => {
    const feedbackTargets = filterTargetsByType(
      selectedTargets,
      TargetTypeEnum.FEEDBACK,
    );

    return new Set(
      feedbackTargets.flatMap(target => {
        const uids = [];
        if (target.uid) {
          uids.push(target.uid);
        }
        if (target.deliverable?.uid) {
          uids.push(target.deliverable.uid);
        }
        if (target.children && target.children.length > 0) {
          target.children.forEach(child => {
            if (child.uid) {
              uids.push(child.uid);
            }
            if (child.deliverable?.uid) {
              uids.push(child.deliverable.uid);
            }
          });
        }
        return uids;
      }),
    );
  }, [selectedTargets]);

  // Handlers
  const handleAcceptTarget = (targetUid: string) => {
    if (
      loadingTargetUids.includes(targetUid) ||
      acceptedTargetUids.includes(targetUid)
    ) {
      return;
    }

    const targetToAccept = allTargets.find(target => target.uid === targetUid);

    if (targetToAccept) {
      setLoadingTargetUids(prev => [...prev, targetUid]);
      mutateAcceptTargets([targetUid]);
    }
  };

  const handleDragStart = (event: any) => {
    const item = event.active.data.current?.data;

    if (item) {
      setDraggedItem(item);
      const isFromSideList = availableDeliverables.some(
        d => d.uid === item.uid,
      );
      setIsDraggingNewItem(isFromSideList);
    }
  };

  const handleDragEnd = (event: any) => {
    const { active, over } = event;

    if (over && over.id === 'selection-area') {
      const deliverable = active.data.current?.data;
      if (
        deliverable &&
        !selectedTargets.find(d => d.uid === deliverable.uid)
      ) {
        setIsEditDrawer(false);
        setDrawerDeliverable(deliverable);
        setIsOpenDrawer(true);
      }
    } else if (over && over.id.toString().includes('target')) {
      const firstTarget = over.data.current?.target as Target;
      const secondTarget = active.data.current?.data as Target;

      if (firstTarget && secondTarget && firstTarget.uid !== secondTarget.uid) {
        actionModal.openModal({
          title: t('common_merge_targets'),
          message: t('common_do_you_really_want_to_merge_the_targets'),
          actions: [
            {
              label: t('common_yes'),
              onClick: () => {},
            },
            {
              label: t('common_no'),
              onClick: actionModal.closeModal,
              variant: 'secondary',
            },
          ],
          onConfirm: async () => {
            const values = { firstTarget, secondTarget };
            await mutateMergeTargets(values);
          },
        });
      }
    }

    setDraggedItem(null);
    setIsDraggingNewItem(false);
  };

  const onDrawerSuccessSubmit = (proposal: Proposal) => {
    const feedbackTargets = filterTargetsByType(
      proposal.targets || [],
      TargetTypeEnum.FEEDBACK,
    );
    setSelectedTargets(feedbackTargets);
  };

  const handleSubmit = () => {
    if (agreementChoice !== null) {
      mutateFinal();
    }
  };

  const handleClearDeliverable = () => {
    actionModal.openModal({
      title: t('common_clear_list'),
      message: t('common_do_you_really_want_to_clear_the_list'),
      actions: [
        {
          label: t('common_yes'),
          onClick: () => {},
        },
        {
          label: t('common_no'),
          onClick: actionModal.closeModal,
          variant: 'secondary',
        },
      ],
      onConfirm: async () => {
        if (isLoadingDelete) {
          return;
        }

        const allTargetUids = selectedTargets.map(target => target.uid);

        setAcceptedTargetUids(prev =>
          prev.filter(uid => !allTargetUids.includes(uid)),
        );

        await mutateDelete(selectedTargets);
      },
    });
  };

  const handleRemoveTargets = async (targets: Target[]) => {
    if (targets.length === 0) {
      return;
    }

    const targetsToDelete = targets.map(target => {
      return { uid: target.uid || '', targetType: TargetTypeEnum.FEEDBACK };
    });

    actionModal.openModal({
      title: t('common_delete'),
      message: t('common_do_you_really_want_to_delete'),
      actions: [
        {
          label: t('common_yes'),
          onClick: () => {},
        },
        {
          label: t('common_no'),
          onClick: actionModal.closeModal,
          variant: 'secondary',
        },
      ],
      onConfirm: async () => {
        if (isLoadingDelete) {
          return;
        }

        const removedTargetUids = targets.map(target => target.uid);

        const proposal = await proposalService.deleteTargets(
          proposalUid,
          targetsToDelete,
        );

        const feedbackTargets = filterTargetsByType(
          proposal.targets || [],
          TargetTypeEnum.FEEDBACK,
        );
        setSelectedTargets(feedbackTargets);

        setAcceptedTargetUids(prev =>
          prev.filter(uid => !removedTargetUids.includes(uid)),
        );

        actionModal.closeModal();
      },
    });
  };

  const handleOpenEditDrawer = (target: Target) => {
    setIsEditDrawer(true);
    setDrawerDeliverable(target);
    setIsOpenDrawer(true);
  };

  const isTargetLoading = (targetUid: string) => {
    return loadingTargetUids.includes(targetUid);
  };

  const handleFillWithProposal = () => {
    // Get all proposal targets that are not already in feedback
    const proposalTargets = filterTargetsByType(
      allTargets,
      TargetTypeEnum.PROPOSAL,
    );

    const targetUidsToAccept = proposalTargets
      .filter(target => target.uid && !acceptedTargetUids.includes(target.uid))
      .map(target => target.uid) as string[];

    if (targetUidsToAccept.length > 0) {
      setLoadingTargetUids(prev => [...prev, ...targetUidsToAccept]);
      mutateAcceptAllTargets(targetUidsToAccept);
    }
  };
  return {
    // States
    selectedTargets,
    acceptedTargetUids,
    loadingTargetUids,
    draggedItem,
    isDraggingNewItem,
    isOpenDrawer,
    isEditDrawer,
    drawerDeliverable,
    totalWeight,
    agreementChoice,

    // Loading states
    isLoading,
    isLoadingDelete,
    isLoadingMergeTargets,
    isLoadingAcceptTargets,
    isLoadingAcceptAllTargets,

    // Handlers
    handleAcceptTarget,
    handleDragStart,
    handleDragEnd,
    handleSubmit,
    handleClearDeliverable,
    handleRemoveTargets,
    onDrawerSuccessSubmit,
    setIsOpenDrawer,
    setDrawerDeliverable,
    handleOpenEditDrawer,
    handleFillWithProposal,
    setAgreementChoice,

    // Computed values
    availableDeliverables,
    selectedFeedbackTargetUids,

    // Helper functions
    filterTargetsByType,
    isTargetLoading,

    // Modal control
    actionModal,
  };
}
