import { useMemo, useState } from 'react';
import { useMutation } from '@tanstack/react-query';
import { useTranslate } from '@tolgee/react';

import { useCatalog } from '~/entities/Home/hooks/useCatalog';
import { useActionModal } from '~/shared/components/ActionModal/useActionModal';
import proposalService from '~/shared/services/proposal';
import targetTypesService from '~/shared/services/targetTypes';
import { DeliverableItem } from '~/shared/types/Deliverable';
import { Proposal } from '~/shared/types/Proposal';
import { Target } from '~/shared/types/Target';
import { ProposalStatusEnum } from '~/shared/utils/enums';
import { TargetTypeEnum } from '~/shared/utils/enums/target-type';

import { filterTargetsByType } from '../../utils/targetFilters';

import { CreateTargetBody, UseFinalParams, UseFinalReturn } from './types';

export function useFinal({
  proposalUid,
  targets,
  allTargets,
  catalogFilterHook,
  onProposalUpdate,
}: UseFinalParams): UseFinalReturn {
  const { t } = useTranslate();
  const actionModal = useActionModal();

  // States
  const [draggedItem, setDraggedItem] = useState<
    DeliverableItem | Target | null
  >(null);
  const [selectedTargets, setSelectedTargets] = useState<Target[]>(
    filterTargetsByType(targets || [], TargetTypeEnum.FINAL),
  );
  const [acceptedTargetUids, setAcceptedTargetUids] = useState<string[]>([]);
  const [loadingTargetUids, setLoadingTargetUids] = useState<string[]>([]);
  const [isDraggingNewItem, setIsDraggingNewItem] = useState(false);
  const [isOpenDrawer, setIsOpenDrawer] = useState<boolean>(false);
  const [drawerDeliverable, setDrawerDeliverable] = useState<
    DeliverableItem | Target
  >();
  const [isEditDrawer, setIsEditDrawer] = useState<boolean>(false);
  const [agreementChoice, setAgreementChoice] = useState<boolean | null>(null);

  const { filters } = catalogFilterHook;

  const totalWeight = useMemo(
    () =>
      selectedTargets.reduce((sum, target) => sum + (target.weight || 0), 0),
    [selectedTargets],
  );

  const { data: deliverables } = useCatalog(1, 100, undefined, filters);

  const { isLoading, mutate: mutateCompleted } = useMutation({
    mutationFn: () => {
      return proposalService.changeProposalStatus(
        proposalUid,
        ProposalStatusEnum.COMPLETED,
      );
    },
    onSuccess: (response: Proposal) => {
      // Proposal completed successfully
      onProposalUpdate?.(response);
    },
    onError: error => {
      console.error(error);
    },
  });

  const { isLoading: isLoadingDelete, mutate: mutateDelete } = useMutation({
    mutationFn: (targets: Target[]) => {
      const targetsToDelete = targets.map(target => {
        return { uid: target.uid || '', targetType: TargetTypeEnum.FINAL };
      });

      return proposalService.deleteTargets(proposalUid, targetsToDelete);
    },
    onSuccess: (proposal: Proposal) => {
      const finalTargets = filterTargetsByType(
        proposal.targets || [],
        TargetTypeEnum.FINAL,
      );
      setSelectedTargets(finalTargets);
      actionModal.closeModal();
    },
    onError: error => {
      console.error(error);
    },
  });

  const getChildForBodyRequest = (child: Target[]) => {
    return child.map(c => ({
      weight: c.weight,
      scope: c.scope,
      uidDeliverable: c.deliverable?.uid || '',
      ...(c.uid && { uid: c.uid }),
    }));
  };

  const { isLoading: isLoadingMergeTargets, mutate: mutateMergeTargets } =
    useMutation({
      mutationFn: (values: { firstTarget: Target; secondTarget: Target }) => {
        const firstTarget = values.firstTarget as Target;
        const secondTarget = values.secondTarget as Target;

        const treatedChildren = [];

        if (firstTarget.children && firstTarget.children.length > 0) {
          treatedChildren.push(...getChildForBodyRequest(firstTarget.children));
        } else {
          treatedChildren.push({
            weight: firstTarget.weight,
            scope: firstTarget.scope,
            uidDeliverable:
              firstTarget.uidDeliverable || firstTarget.deliverable?.uid || '',
            ...(firstTarget.uid && { uid: firstTarget.uid }),
          });
        }

        if (secondTarget.children && secondTarget.children.length > 0) {
          treatedChildren.push(
            ...getChildForBodyRequest(secondTarget.children),
          );
        } else {
          treatedChildren.push({
            weight: secondTarget.weight,
            scope: secondTarget.scope,
            uidDeliverable:
              secondTarget.uidDeliverable ||
              secondTarget.deliverable?.uid ||
              '',
            ...(secondTarget.uid && { uid: secondTarget.uid }),
          });
        }

        const data: CreateTargetBody = {
          targets: [
            {
              weight: (firstTarget.weight || 0) + (secondTarget.weight || 0),
              targetType: TargetTypeEnum.FINAL,
              children: treatedChildren,
            },
          ],
        };

        return proposalService.createTarget(proposalUid, data);
      },
      onSuccess: (proposal: Proposal) => {
        const finalTargets = filterTargetsByType(
          proposal.targets || [],
          TargetTypeEnum.FINAL,
        );
        setSelectedTargets(finalTargets);
        actionModal.closeModal();
      },
      onError: error => {
        console.error(error);
      },
    });

  const { isLoading: isLoadingAcceptTargets, mutate: mutateAcceptTargets } =
    useMutation({
      mutationFn: (targetUids: string[]) => {
        return targetTypesService.addFinalToTargets(
          targetUids,
          agreementChoice ?? undefined,
        );
      },
      onSuccess: (response, targetUids) => {
        const targetUid = targetUids[0];
        const targetToAccept = allTargets.find(
          target => target.uid === targetUid,
        );

        if (targetToAccept) {
          const finalTarget: Target = {
            ...targetToAccept,
            targetTypes: [
              ...(targetToAccept.targetTypes || []),
              { type: TargetTypeEnum.FINAL },
            ],
            children:
              targetToAccept.children?.map(child => ({
                ...child,
                targetTypes: child.targetTypes?.some(
                  tt => tt.type === TargetTypeEnum.FINAL,
                )
                  ? child.targetTypes
                  : [
                      ...(child.targetTypes || []),
                      { type: TargetTypeEnum.FINAL },
                    ],
              })) || [],
          };

          setSelectedTargets(prev => {
            const existingTargetIndex = prev.findIndex(
              t => t.uid === targetUid,
            );

            if (existingTargetIndex >= 0) {
              const updatedTargets = [...prev];
              updatedTargets[existingTargetIndex] = finalTarget;
              return filterTargetsByType(updatedTargets, TargetTypeEnum.FINAL);
            } else {
              const updatedTargets = [...prev, finalTarget];
              return filterTargetsByType(updatedTargets, TargetTypeEnum.FINAL);
            }
          });

          setAcceptedTargetUids(prev => [...prev, targetUid]);
        }

        setLoadingTargetUids(prev => prev.filter(uid => uid !== targetUid));
      },
      onError: (error, targetUids) => {
        console.error('Error accepting targets:', error);
        const targetUid = targetUids[0];
        setLoadingTargetUids(prev => prev.filter(uid => uid !== targetUid));
      },
    });

  const {
    isLoading: isLoadingAcceptAllTargets,
    mutate: mutateAcceptAllTargets,
  } = useMutation({
    mutationFn: (targetUids: string[]) => {
      return targetTypesService.addFinalToTargets(
        targetUids,
        agreementChoice ?? undefined,
      );
    },
    onSuccess: (_, targetUids) => {
      const targetsToAccept = allTargets.filter(target =>
        targetUids.includes(target.uid || ''),
      );

      const finalTargets = targetsToAccept.map(targetToAccept => ({
        ...targetToAccept,
        targetTypes: [
          ...(targetToAccept.targetTypes || []),
          { type: TargetTypeEnum.FINAL },
        ],
        children:
          targetToAccept.children?.map(child => ({
            ...child,
            targetTypes: child.targetTypes?.some(
              tt => tt.type === TargetTypeEnum.FINAL,
            )
              ? child.targetTypes
              : [...(child.targetTypes || []), { type: TargetTypeEnum.FINAL }],
          })) || [],
      }));

      setSelectedTargets(prev => {
        const updatedTargets = [...prev];

        finalTargets.forEach(finalTarget => {
          const existingTargetIndex = updatedTargets.findIndex(
            t => t.uid === finalTarget.uid,
          );

          if (existingTargetIndex >= 0) {
            updatedTargets[existingTargetIndex] = finalTarget;
          } else {
            updatedTargets.push(finalTarget);
          }
        });

        return filterTargetsByType(updatedTargets, TargetTypeEnum.FINAL);
      });

      setAcceptedTargetUids(prev => [...prev, ...targetUids]);
      setLoadingTargetUids(prev =>
        prev.filter(uid => !targetUids.includes(uid)),
      );
    },
    onError: (error, targetUids) => {
      console.error('Error accepting all targets:', error);
      setLoadingTargetUids(prev =>
        prev.filter(uid => !targetUids.includes(uid)),
      );
    },
  });

  // Computed values
  const availableDeliverables = useMemo(() => {
    const finalTargets = filterTargetsByType(
      selectedTargets,
      TargetTypeEnum.FINAL,
    );

    const selectedUids = new Set(
      finalTargets.flatMap(d => {
        if (d.children && d.children.length > 0) {
          return d.children
            .map(child => child.deliverable?.uid)
            .filter(Boolean);
        } else {
          return d.deliverable?.uid ? [d.deliverable.uid] : [];
        }
      }),
    );

    const deliverableItems = deliverables?.data || [];
    return deliverableItems.filter(d => !selectedUids.has(d.uid));
  }, [deliverables, selectedTargets]);

  const selectedFinalTargetUids = useMemo(() => {
    const finalTargets = filterTargetsByType(
      selectedTargets,
      TargetTypeEnum.FINAL,
    );

    return new Set(
      finalTargets.flatMap(target => {
        const uids = [];
        if (target.uid) {
          uids.push(target.uid);
        }
        if (target.deliverable?.uid) {
          uids.push(target.deliverable.uid);
        }
        if (target.children && target.children.length > 0) {
          target.children.forEach(child => {
            if (child.uid) {
              uids.push(child.uid);
            }
            if (child.deliverable?.uid) {
              uids.push(child.deliverable.uid);
            }
          });
        }
        return uids;
      }),
    );
  }, [selectedTargets]);

  // Handlers
  const handleAcceptTarget = (targetUid: string) => {
    if (
      loadingTargetUids.includes(targetUid) ||
      acceptedTargetUids.includes(targetUid)
    ) {
      return;
    }

    const targetToAccept = allTargets.find(target => target.uid === targetUid);

    if (targetToAccept) {
      setLoadingTargetUids(prev => [...prev, targetUid]);
      mutateAcceptTargets([targetUid]);
    }
  };

  const handleDragStart = (event: any) => {
    const item = event.active.data.current?.data;

    if (item) {
      setDraggedItem(item);
      const isFromSideList = availableDeliverables.some(
        d => d.uid === item.uid,
      );
      setIsDraggingNewItem(isFromSideList);
    }
  };

  const handleDragEnd = (event: any) => {
    const { active, over } = event;

    if (over && over.id === 'selection-area') {
      const deliverable = active.data.current?.data;

      if (
        deliverable &&
        !selectedTargets.find(d => d.uid === deliverable.uid)
      ) {
        setIsEditDrawer(false);
        setDrawerDeliverable(deliverable);
        setIsOpenDrawer(true);
      }
    } else if (over && over.id.toString().includes('target')) {
      const firstTarget = over.data.current?.target as Target;
      const secondTarget = active.data.current?.data as Target;

      if (firstTarget && secondTarget && firstTarget.uid !== secondTarget.uid) {
        actionModal.openModal({
          title: t('common_merge_targets'),
          message: t('common_do_you_really_want_to_merge_the_targets'),
          actions: [
            {
              label: t('common_yes'),
              onClick: () => {},
            },
            {
              label: t('common_no'),
              onClick: actionModal.closeModal,
              variant: 'secondary',
            },
          ],
          onConfirm: async () => {
            const values = { firstTarget, secondTarget };
            await mutateMergeTargets(values);
          },
        });
      }
    }

    setDraggedItem(null);
    setIsDraggingNewItem(false);
  };

  const onDrawerSuccessSubmit = (proposal: Proposal) => {
    const finalTargets = filterTargetsByType(
      proposal.targets || [],
      TargetTypeEnum.FINAL,
    );
    setSelectedTargets(finalTargets);
  };

  const handleSubmit = () => {
    if (agreementChoice !== null) {
      mutateCompleted();
    }
  };

  const handleClearDeliverable = () => {
    actionModal.openModal({
      title: t('common_clear_list'),
      message: t('common_do_you_really_want_to_clear_the_list'),
      actions: [
        {
          label: t('common_yes'),
          onClick: () => {},
        },
        {
          label: t('common_no'),
          onClick: actionModal.closeModal,
          variant: 'secondary',
        },
      ],
      onConfirm: async () => {
        if (isLoadingDelete) {
          return;
        }

        const targetsToDelete = selectedTargets.map(target => ({
          uid: target.uid || '',
          targetType: TargetTypeEnum.FINAL,
        }));

        const deletedTargetUids = selectedTargets
          .map(target => target.uid)
          .filter(uid => uid) as string[];
        setAcceptedTargetUids(prevAccepted =>
          prevAccepted.filter(uid => !deletedTargetUids.includes(uid)),
        );

        const proposal = await proposalService.deleteTargets(
          proposalUid,
          targetsToDelete,
        );

        const finalTargets = filterTargetsByType(
          proposal.targets || [],
          TargetTypeEnum.FINAL,
        );
        setSelectedTargets(finalTargets);
        actionModal.closeModal();
      },
    });
  };

  const handleRemoveTargets = (targets: Target[]) => {
    const targetsToDelete = targets.map(target => ({
      uid: target.uid || '',
      targetType: TargetTypeEnum.FINAL,
    }));

    actionModal.openModal({
      title: t('common_remove_targets'),
      message: t('common_do_you_really_want_to_remove_the_targets'),
      actions: [
        {
          label: t('common_yes'),
          onClick: () => {},
        },
        {
          label: t('common_no'),
          onClick: actionModal.closeModal,
          variant: 'secondary',
        },
      ],
      onConfirm: async () => {
        if (isLoadingDelete) {
          return;
        }

        const removedTargetUids = targets.map(target => target.uid);

        const proposal = await proposalService.deleteTargets(
          proposalUid,
          targetsToDelete,
        );

        const finalTargets = filterTargetsByType(
          proposal.targets || [],
          TargetTypeEnum.FINAL,
        );
        setSelectedTargets(finalTargets);

        setAcceptedTargetUids(prev =>
          prev.filter(uid => !removedTargetUids.includes(uid)),
        );

        actionModal.closeModal();
      },
    });
  };

  const handleOpenEditDrawer = (target: Target) => {
    setIsEditDrawer(true);
    setDrawerDeliverable(target);
    setIsOpenDrawer(true);
  };

  const isTargetLoading = (targetUid: string) => {
    return loadingTargetUids.includes(targetUid);
  };

  const handleFillWithProposal = () => {
    const proposalTargets = filterTargetsByType(
      allTargets,
      TargetTypeEnum.PROPOSAL,
    );

    const currentFinalTargetUids = new Set(
      selectedTargets.map(target => target.uid).filter(Boolean),
    );

    const targetUidsToAccept = proposalTargets
      .filter(
        target =>
          target.uid &&
          !acceptedTargetUids.includes(target.uid) &&
          !currentFinalTargetUids.has(target.uid),
      )
      .map(target => target.uid) as string[];

    if (targetUidsToAccept.length > 0) {
      setLoadingTargetUids(prev => [...prev, ...targetUidsToAccept]);
      mutateAcceptAllTargets(targetUidsToAccept);
    }
  };

  const handleFillWithFeedback = () => {
    const feedbackTargets = filterTargetsByType(
      allTargets,
      TargetTypeEnum.FEEDBACK,
    );

    const currentFinalTargetUids = new Set(
      selectedTargets.map(target => target.uid).filter(Boolean),
    );

    const targetUidsToAccept = feedbackTargets
      .filter(
        target =>
          target.uid &&
          !acceptedTargetUids.includes(target.uid) &&
          !currentFinalTargetUids.has(target.uid),
      )
      .map(target => target.uid) as string[];

    if (targetUidsToAccept.length > 0) {
      setLoadingTargetUids(prev => [...prev, ...targetUidsToAccept]);
      mutateAcceptAllTargets(targetUidsToAccept);
    }
  };

  return {
    // States
    selectedTargets,
    acceptedTargetUids,
    loadingTargetUids,
    draggedItem,
    isDraggingNewItem,
    isOpenDrawer,
    isEditDrawer,
    drawerDeliverable,
    totalWeight,
    agreementChoice,

    // Loading states
    isLoading,
    isLoadingDelete,
    isLoadingMergeTargets,
    isLoadingAcceptTargets,
    isLoadingAcceptAllTargets,

    // Handlers
    handleAcceptTarget,
    handleDragStart,
    handleDragEnd,
    handleSubmit,
    handleClearDeliverable,
    handleRemoveTargets,
    onDrawerSuccessSubmit,
    setIsOpenDrawer,
    setDrawerDeliverable,
    handleOpenEditDrawer,
    handleFillWithProposal,
    handleFillWithFeedback,
    setAgreementChoice,

    // Computed values
    availableDeliverables,
    selectedFinalTargetUids,

    // Helper functions
    filterTargetsByType,
    isTargetLoading,

    // Modal control
    actionModal,
  };
}
