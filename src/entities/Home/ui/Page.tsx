import React from 'react';
import Head from 'next/head';
import { Container, Typography } from '@ghq-abi/design-system-v2';
import { useTranslate } from '@tolgee/react';

import { ContentPage } from '~/app/templates/ContentPage';
import { Catchball } from '~/entities/Home/components/Catchball';
import { DeliverableItemsResponse } from '~/shared/types/DeliverablesService';
import { ProposalItemsResponse } from '~/shared/types/ProposalService';

interface PageProps {
  initialKpis: DeliverableItemsResponse;
  initialProposals: ProposalItemsResponse;
}

export function Page({ initialKpis, initialProposals }: PageProps) {
  const { t } = useTranslate();

  return (
    <>
      <Head>
        <title>{t('home.title')}</title>
      </Head>

      <ContentPage
        css={{
          '@lg': { pt: 0, px: '$md' },
        }}
        contentCss={{ bg: '$gray100', p: 0, gap: '$5' }}
        transparentContent
      >
        <Container className="flex flex-col gap-4">
          <Container className="flex flex-col gap-2">
            <Typography variant="title-md-bold">
              {t('common_catchball')}
            </Typography>
            <Typography variant="body-sm-medium">
              {t('common_catchball_description')}
            </Typography>
          </Container>
          <Catchball
            initialKpis={initialKpis}
            initialProposals={initialProposals}
          />
        </Container>
      </ContentPage>
    </>
  );
}
