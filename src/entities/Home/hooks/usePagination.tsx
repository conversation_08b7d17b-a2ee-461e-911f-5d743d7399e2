import { useState } from 'react';

interface UsePaginationProps {
  initialPageSize?: number;
  totalRecords?: number;
}

interface UsePaginationReturn {
  pageNumber: number;
  pageSize: number;
  totalPages: number;
  currentPage: number;
  setPageNumber: (page: number) => void;
}

export function usePagination({
  initialPageSize = 25,
  totalRecords = 0,
}: UsePaginationProps = {}): UsePaginationReturn {
  const [pageNumber, setPageNumber] = useState<number>(1);
  const pageSize = initialPageSize;

  const totalPages = Math.ceil((totalRecords || 0) / (pageSize || 1));
  const currentPage = pageNumber || 1;

  return {
    pageNumber,
    pageSize,
    totalPages,
    currentPage,
    setPageNumber,
  };
}
