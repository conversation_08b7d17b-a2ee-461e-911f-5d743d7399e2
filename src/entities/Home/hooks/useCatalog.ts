import { useEffect, useMemo, useRef, useState } from 'react';
import { useQuery } from '@tanstack/react-query';

import {
  CatalogFilterOptions,
  DeliverableType,
} from '~/entities/Home/components/Catalog/types';
import deliverablesService from '~/shared/services/deliverables';
import { DeliverableItemsResponse } from '~/shared/types/DeliverablesService';
import { GetDeliverableFilters } from '~/shared/types/GetDeliverableFilters';

const PAGE_SIZE = 100;

const mapFiltersToApiParams = (
  filters: CatalogFilterOptions,
  pageNumber: number = 1,
  pageSize: number = PAGE_SIZE,
): GetDeliverableFilters => {
  const apiFilters: GetDeliverableFilters = {
    pageNumber,
    pageSize,
    ...(filters.fuzzy_search && { search: filters.fuzzy_search }),
    ...(filters.businessFunctions?.length
      ? { businessFunctions: filters.businessFunctions }
      : {}),
    ...(filters.isActive !== undefined ? { isActive: filters.isActive } : {}),
    ...(filters.orderBy !== undefined ? { orderBy: filters.orderBy } : {}),
  };

  const selectedTypes = filters.deliverableTypes
    ? Array.isArray(filters.deliverableTypes)
      ? filters.deliverableTypes
      : [filters.deliverableTypes]
    : [];

  if (selectedTypes.length) {
    const typeMap: Record<string, DeliverableType[]> = {
      KPI: [DeliverableType.KPI],
      PROJECT: [DeliverableType.PROJECT],
    };

    const deliverableTypes = selectedTypes.flatMap(t => typeMap[t] ?? []);
    if (deliverableTypes.length) {
      apiFilters.deliverableTypes = deliverableTypes;
    }
  } else {
    apiFilters.deliverableTypes = [
      DeliverableType.KPI,
      DeliverableType.PROJECT,
    ];

    if (filters.isActive === undefined) {
      apiFilters.isActive = true;
    }
  }

  return apiFilters;
};

const hasAnyActiveFilters = (
  filters: CatalogFilterOptions,
  pageNumber: number,
) =>
  Boolean(
    filters.fuzzy_search ||
      filters.businessFunctions?.length ||
      filters.isActive !== undefined ||
      filters.deliverableTypes?.length ||
      filters.orderBy ||
      pageNumber !== 1,
  );

export const useCatalog = (
  pageNumber: number = 1,
  pageSize: number = PAGE_SIZE,
  initialKpis?: DeliverableItemsResponse,
  externalFilters?: CatalogFilterOptions,
) => {
  const isInitialLoad = useRef(true);

  const effectiveFiltersForQuery = useMemo(() => {
    const { stagedBusinessFunctions, ...rest } = externalFilters ?? {};
    return rest;
  }, [externalFilters]);

  const [hasFiltersBeenUsed, setHasFiltersBeenUsed] = useState(false);
  const hasActiveFilters = useMemo(
    () => hasAnyActiveFilters(externalFilters ?? {}, pageNumber),
    [externalFilters, pageNumber],
  );

  useEffect(() => {
    if (hasActiveFilters && !hasFiltersBeenUsed) {
      setHasFiltersBeenUsed(true);
    }
  }, [hasActiveFilters, hasFiltersBeenUsed]);

  const shouldUseInitialData = Boolean(
    initialKpis?.data?.length &&
      !hasFiltersBeenUsed &&
      pageNumber === 1 &&
      pageSize === PAGE_SIZE &&
      !externalFilters?.fuzzy_search &&
      !externalFilters?.businessFunctions?.length &&
      externalFilters?.isActive === undefined &&
      !externalFilters?.deliverableTypes?.length &&
      !externalFilters?.orderBy,
  );

  const { data, isLoading, isError, isFetching } = useQuery({
    queryKey: ['catalog', effectiveFiltersForQuery, PAGE_SIZE],
    queryFn: ({ pageParam = 1 }) => {
      const apiFilters = mapFiltersToApiParams(
        effectiveFiltersForQuery,
        pageParam as number,
        PAGE_SIZE,
      );
      return deliverablesService.getDeliverables(apiFilters);
    },
    initialData: shouldUseInitialData ? initialKpis : undefined,
    onSuccess: () => {
      isInitialLoad.current = false;
    },
  });

  const isInitialLoading = isLoading && isInitialLoad.current;
  const isSearchLoading = isFetching && !isInitialLoad.current;

  const finalData = shouldUseInitialData ? initialKpis : data;

  return {
    data: finalData,
    isLoading,
    isInitialLoading,
    isSearchLoading,
    isError,
  };
};
