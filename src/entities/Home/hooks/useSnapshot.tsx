import { RefObject, useState } from 'react';
import { toPng } from 'html-to-image';

export function useSnapshot() {
  const [loading, setLoading] = useState(false);

  const handleTakeSnapshot = async (
    element: RefObject<HTMLDivElement>,
    name: string,
  ) => {
    if (!element.current) {
      return null;
    }

    setLoading(true);
    try {
      try {
        const dataUrl = await toPng(element.current, {
          includeQueryParams: true,
          backgroundColor: '#F5F5F5',
        });

        const link = document.createElement('a');
        link.download = `${name}.png`;
        link.href = dataUrl;
        link.click();

        setLoading(false);
      } catch (error) {
        console.error('Error converting to image:', error);
      }
    } catch (error) {
      console.error('Error taking snapshot:', error);
      setLoading(false);
      return null;
    }
  };

  return { loading, handleTakeSnapshot };
}
