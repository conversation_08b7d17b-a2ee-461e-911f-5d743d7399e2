import React from 'react';
import { Container } from '@ghq-abi/design-system-v2';

import { TabEmptyState } from '~/entities/Proposal/components/TabEmptyState';
import { ChildCard, TargetCard } from '~/shared/components/TargetCard';
import { Target } from '~/shared/types/Target';
import { ProposalStatusEnum, TargetTypeEnum } from '~/shared/utils/enums';

interface ProposalTargetOnlyProps {
  targets: Target[];
  proposalUid: string;
  proposalStatus?: ProposalStatusEnum;
  hasManagerPermission?: boolean;
  hasEmployeePermission?: boolean;
  isDrawer?: boolean;
  isReadyOnly?: boolean;
}

export const ProposalTargetOnly = ({
  targets,
  proposalStatus,
  hasManagerPermission,
  hasEmployeePermission,
  isDrawer,
}: ProposalTargetOnlyProps) => {
  const proposalTargets =
    targets.length > 0
      ? targets.filter(target =>
          target.targetTypes?.some(
            targetType => targetType.type === TargetTypeEnum.PROPOSAL,
          ),
        )
      : [];

  if (proposalTargets.length === 0 && hasEmployeePermission) {
    return (
      <TabEmptyState
        title="No proposals available"
        description="There are no proposal targets to display at this time."
      />
    );
  }

  return (
    <Container className="flex flex-col gap-4">
      {proposalTargets.map(target => {
        if (target.children && target.children.length > 1) {
          return (
            <TargetCard
              key={target.uid}
              data={target}
              proposalStatus={proposalStatus}
              hideChildren={target.children.length <= 1}
              currentTargetType={TargetTypeEnum.PROPOSAL}
              hasManagerPermission={hasManagerPermission}
              hasEmployeePermission={hasEmployeePermission}
              isDrawer={isDrawer}
            />
          );
        }
        return (
          <ChildCard
            key={target.uid}
            target={target}
            disableDrag={true}
            hasManagerPermission={hasManagerPermission}
            hasEmployeePermission={hasEmployeePermission}
            isDrawer={isDrawer}
          />
        );
      })}
    </Container>
  );
};
