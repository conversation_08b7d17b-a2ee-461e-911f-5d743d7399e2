export interface ProposalFilterOptions {
  status?: string[];
  zones?: string[];
  businessFunctions?: string[];
  fuzzy_search?: string;
  sltLevel?: string[];
  sltName?: string[];
  stagedBusinessFunctions?: string[];
  stagedZones?: string[];
  stagedSltLevel?: string[];
  stagedStatus?: string[];
  stagedSltName?: string[];
}

export type ProposalFilterProps = {
  searchQuery: string;
  handleSearchChange: (query: string) => void;
  handleSearchKeyPress: (e: React.KeyboardEvent) => void;
  handleToggle: (key: string, propName: string) => void;
  selectedFunctions: string[];
  selectedStatus: string[];
  selectedZones: string[];
  selectedSltLevel: string[];
  selectedSltName: string[];
  handleSearchSubmit: () => void;
};
