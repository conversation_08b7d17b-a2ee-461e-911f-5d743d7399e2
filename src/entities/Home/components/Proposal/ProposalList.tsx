import { Dispatch, SetStateAction, useState } from 'react';
import { Container, Skeleton, Typography } from '@ghq-abi/design-system-v2';
import { useTranslate } from '@tolgee/react';

import { useProposal } from '~/entities/Home/hooks/useProposal';
import { CreateTargetDrawer } from '~/entities/Proposal/components/Proposal/CreateTargetDrawer';
import { NoResults, Pagination } from '~/shared/components';
import { usePagination } from '~/shared/hooks';
import { useGetProposal } from '~/shared/hooks/useGetProposal';
import { DeliverableItem } from '~/shared/types/Deliverable';
import { ProposalItemsResponse } from '~/shared/types/ProposalService';
import { ProposalStatusEnum, TargetTypeEnum } from '~/shared/utils/enums';

import { useProposalFilter } from '../../hooks/useProposalFilter';

import { ProposalCard } from './ProposalCard';
import { ProposalFilter } from './ProposalFilter';
import { ProposalTargetDrawer } from './ProposalTargetDrawer';

export function ProposalList({
  initialProposals,
  draggedDeliverable,
  isOpenCreateTargetDrawer,
  setIsOpenCreateTargetDrawer,
  proposalIdDragged,
  proposalStatus,
}: {
  initialProposals?: ProposalItemsResponse;
  draggedDeliverable?: DeliverableItem | null;
  isOpenCreateTargetDrawer: boolean;
  setIsOpenCreateTargetDrawer: Dispatch<SetStateAction<boolean>>;
  proposalIdDragged: string | undefined;
  proposalStatus: ProposalStatusEnum | undefined;
}) {
  const { t } = useTranslate();
  const [isOpenDrawer, setIsOpenDrawer] = useState(false);
  const [proposalId, setProposalId] = useState<string | undefined>();
  const proposalFilterHook = useProposalFilter();
  const { filters } = proposalFilterHook;

  const { totalPages, currentPage, setPageNumber, pageSize } = usePagination({
    initialPageSize: 9,
    totalRecords: 0,
  });

  const { proposals, isInitialLoading, isSearchLoading, isError } = useProposal(
    currentPage,
    pageSize,
    initialProposals,
    filters,
  );

  const actualTotalPages = proposals?.totalRecords
    ? Math.ceil(proposals.totalRecords / pageSize)
    : totalPages;

  const { data: proposal, isLoading: isProposalLoading } =
    useGetProposal(proposalId);

  const MAP_PROPOSAL_STATUS_TO_TARGET_TYPE = {
    [ProposalStatusEnum.NOT_STARTED]: TargetTypeEnum.PROPOSAL,
    [ProposalStatusEnum.IN_PROGRESS_PROPOSAL]: TargetTypeEnum.PROPOSAL,
    [ProposalStatusEnum.IN_PROGRESS_FEEDBACK]: TargetTypeEnum.FEEDBACK,
    [ProposalStatusEnum.IN_PROGRESS_FINAL]: TargetTypeEnum.FINAL,
  };

  const targetType = proposalStatus
    ? MAP_PROPOSAL_STATUS_TO_TARGET_TYPE[
        proposalStatus as keyof typeof MAP_PROPOSAL_STATUS_TO_TARGET_TYPE
      ]
    : TargetTypeEnum.PROPOSAL;

  return (
    <Container className="flex flex-col gap-4">
      <ProposalFilter {...proposalFilterHook} />
      {isError && (
        <Container className="flex justify-center items-center py-8">
          <Typography variant="metadata-sm-medium">
            {t('common_something_went_wrong')}
          </Typography>
        </Container>
      )}
      <Container className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {isInitialLoading || isSearchLoading
          ? Array.from({ length: 10 }).map((_, index) => (
              <Skeleton key={index} className="w-full h-[168px]" />
            ))
          : proposals?.data &&
            proposals?.data.length > 0 &&
            proposals?.data.map(proposal => (
              <ProposalCard
                key={proposal.uid}
                data={proposal}
                showActions
                showDrawerAction
                showDetailAction
                onDrawerActionClick={() => {
                  setIsOpenDrawer(true);
                  setProposalId(proposal.uid);
                }}
                draggedDeliverable={draggedDeliverable}
              />
            ))}
      </Container>
      {!isInitialLoading &&
        !isSearchLoading &&
        proposals?.data &&
        proposals?.data.length === 0 && (
          <Container className="flex justify-center items-center py-8">
            <NoResults />
          </Container>
        )}
      <Container className="flex justify-center py-4 mt-8">
        <Pagination
          currentPage={currentPage}
          totalPages={actualTotalPages}
          onPageChange={setPageNumber}
        />
      </Container>
      <ProposalTargetDrawer
        isOpen={isOpenDrawer}
        onClose={() => setIsOpenDrawer(false)}
        proposal={proposal!}
        hasEmployeePermission
        isLoading={isProposalLoading}
      />
      <CreateTargetDrawer
        className="h-full rounded-lb-none"
        proposalId={proposalIdDragged!}
        data={draggedDeliverable!}
        isOpen={isOpenCreateTargetDrawer}
        isEdit={false}
        isDraggingCatalog
        filters={filters}
        onClose={() => {
          setIsOpenCreateTargetDrawer(false);
          setPageNumber(1);
        }}
        onSuccessSubmit={() => {}}
        targetType={targetType}
      />
    </Container>
  );
}
