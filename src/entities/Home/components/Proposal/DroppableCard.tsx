import React from 'react';
import { useDroppable } from '@dnd-kit/core';

import { DeliverableItem } from '~/shared/types/Deliverable';
import { Proposal } from '~/shared/types/Proposal';
import { ProposalStatusEnum } from '~/shared/utils/enums';

export interface DroppableTargetProps {
  children: React.ReactNode;
  deliverable?: DeliverableItem | null;
  isDragging?: boolean;
  proposal?: Proposal;
}

export function DroppableCard({
  children,
  deliverable,
  proposal,
}: DroppableTargetProps) {
  const { setNodeRef, isOver } = useDroppable({
    id: `proposal-${proposal?.uid}`,
    data: {
      type: 'proposal',
      proposal,
      deliverable,
    },
  });

  const STATUS_NOT_DROPPALE = [
    ProposalStatusEnum.IN_PROGRESS_FEEDBACK,
    ProposalStatusEnum.COMPLETED,
  ];

  return (
    <div
      ref={setNodeRef}
      style={{
        backgroundColor: isOver ? '#f0f0f0' : undefined,
        border: isOver
          ? !STATUS_NOT_DROPPALE.includes(
              proposal?.status as ProposalStatusEnum,
            )
            ? '2px dashed #007bff'
            : '2px dashed red'
          : undefined,
        borderRadius: isOver ? '8px' : undefined,
        transition: 'all 0.2s ease-in-out',
      }}
    >
      {children}
    </div>
  );
}
