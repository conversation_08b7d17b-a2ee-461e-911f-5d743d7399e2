import { useState } from 'react';
import {
  DndContext,
  DragEndEvent,
  DragStartEvent,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import { Container } from '@ghq-abi/design-system-v2';
import { useTranslate } from '@tolgee/react';

import { ActionModal } from '~/shared/components/ActionModal/ActionModal';
import { useActionModal } from '~/shared/components/ActionModal/useActionModal';
import { DeliverableItem } from '~/shared/types/Deliverable';
import { DeliverableItemsResponse } from '~/shared/types/DeliverablesService';
import { ProposalItemsResponse } from '~/shared/types/ProposalService';
import { ProposalStatusEnum } from '~/shared/utils/enums';

import { CatalogList } from './Catalog/CatalogList';
import { ProposalList } from './Proposal/ProposalList';

interface CatchballProps {
  initialKpis: DeliverableItemsResponse;
  initialProposals: ProposalItemsResponse;
}

export function Catchball({ initialKpis, initialProposals }: CatchballProps) {
  const { t } = useTranslate();
  const [draggedDeliverable, setDraggedDeliverable] =
    useState<DeliverableItem | null>(null);
  const [isOpenCreateTargetDrawer, setIsOpenCreateTargetDrawer] =
    useState(false);
  const [proposalId, setProposalId] = useState<string | undefined>();
  const [proposalStatus, setProposalStatus] = useState<
    ProposalStatusEnum | undefined
  >();
  const actionModal = useActionModal();
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 3,
      },
    }),
  );

  const STATUS_NOT_DROPPALE = [
    ProposalStatusEnum.IN_PROGRESS_FEEDBACK,
    ProposalStatusEnum.COMPLETED,
  ];

  const handleDragStart = (event: DragStartEvent) => {
    const deliverable = event.active.data.current?.data;
    if (deliverable) {
      setDraggedDeliverable(deliverable);
    }
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { over } = event;

    if (!over) {
      setDraggedDeliverable(null);
      return;
    }

    const overData = over.data.current;
    const droppedProposal = overData?.proposal;
    const dropType = overData?.type;

    // TODO: Add Manager permission
    if (dropType === 'proposal' && droppedProposal) {
      if (
        STATUS_NOT_DROPPALE.includes(
          droppedProposal.status as ProposalStatusEnum,
        )
      ) {
        setDraggedDeliverable(null);

        actionModal.openModal({
          title: t('common_pay_attention'),
          message: t('common_catchball_target_not_assign_proposal'),
          actions: [
            {
              label: t('common_understood'),
              onClick: actionModal.handleUnderstood,
              variant: 'primary',
            },
          ],
          onConfirm: () => {
            setIsOpenCreateTargetDrawer(false);
          },
        });
        return;
      } else {
        setIsOpenCreateTargetDrawer(true);
        setProposalId(droppedProposal.uid);
        setProposalStatus(droppedProposal.status);
      }
    }
  };

  return (
    <DndContext
      sensors={sensors}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
    >
      <Container className="flex h-[calc(100vh-240px)] gap-6">
        <CatalogList
          title={t('common_catalog')}
          initialKpis={initialKpis}
          draggedDeliverable={draggedDeliverable}
        />
        <ProposalList
          initialProposals={initialProposals}
          draggedDeliverable={draggedDeliverable}
          isOpenCreateTargetDrawer={isOpenCreateTargetDrawer}
          setIsOpenCreateTargetDrawer={setIsOpenCreateTargetDrawer}
          proposalIdDragged={proposalId}
          proposalStatus={proposalStatus}
        />
      </Container>
      <ActionModal
        isOpen={actionModal.isOpen}
        openModal={actionModal.openModal}
        closeModal={actionModal.closeModal}
        title={actionModal.title}
        message={actionModal.message}
        actions={[
          {
            label: t('common_understood'),
            onClick: actionModal.handleUnderstood,
            variant: 'primary',
          },
        ]}
      />
    </DndContext>
  );
}
