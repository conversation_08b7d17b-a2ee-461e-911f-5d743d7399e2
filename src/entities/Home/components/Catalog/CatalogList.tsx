import React, { useState } from 'react';
import {
  DragEndEvent,
  DragOverlay,
  DragStartEvent,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import { Container, Skeleton, Typography } from '@ghq-abi/design-system-v2';
import { useTranslate } from '@tolgee/react';

import { useCatalog } from '~/entities/Home/hooks/useCatalog';
import { useCatalogFilter } from '~/entities/Home/hooks/useCatalogFilter';
import { NoResults } from '~/shared/components';
import { DeliverableItem } from '~/shared/types/Deliverable';
import { DeliverableItemsResponse } from '~/shared/types/DeliverablesService';

import { CatalogCard } from './CatalogCard';
import { CatalogFilter } from './CatalogFilter';

export interface CatalogListProps {
  title?: string;
  subtitle?: string;
  className?: string;
  containerClassName?: string;
  initialKpis?: DeliverableItemsResponse;
  draggedDeliverable?: DeliverableItem | null;
}

export function CatalogList({
  title,
  subtitle,
  className = '',
  containerClassName = 'w-96 min-w-96 bg-white rounded-lg p-6 flex flex-col',
  initialKpis,
  draggedDeliverable,
}: CatalogListProps) {
  const { t } = useTranslate();

  const catalogFilterHook = useCatalogFilter();
  const { filters } = catalogFilterHook;

  const {
    data: deliverables,
    isSearchLoading,
    isInitialLoading,
    isError,
  } = useCatalog(1, 100, initialKpis, filters);

  return (
    <div className={`${containerClassName} ${className}`}>
      <div className="flex flex-col mb-4 flex-shrink-0">
        {(title || subtitle) && (
          <>
            {isInitialLoading && title && (
              <Skeleton className="w-full h-6 mb-2" />
            )}
            {!isInitialLoading && title && (
              <Typography variant="body-sm-bold" className="text-gray-900 mb-2">
                {title}
              </Typography>
            )}

            {isInitialLoading && subtitle && (
              <Skeleton className="w-full h-4 mb-2" />
            )}
            {!isInitialLoading && subtitle && (
              <Typography
                variant="body-sm-regular"
                className="text-gray-500 mb-4"
              >
                {subtitle}
              </Typography>
            )}
          </>
        )}

        {isInitialLoading &&
          Array.from({ length: 2 }).map((_, index) => (
            <Skeleton key={index} className="w-full h-10 mb-4" />
          ))}
        {!isInitialLoading && <CatalogFilter {...catalogFilterHook} />}
      </div>

      <div className="space-y-3 flex-1 min-h-0 overflow-y-auto">
        {isError && (
          <Container className="flex justify-center items-center py-8">
            <Typography variant="metadata-sm-medium">
              {t('common_something_went_wrong')}
            </Typography>
          </Container>
        )}
        {isInitialLoading || isSearchLoading ? (
          Array.from({ length: 4 }).map((_, index) => (
            <Skeleton key={index} className="w-full h-[120px]" />
          ))
        ) : deliverables?.data?.length && deliverables.data.length > 0 ? (
          deliverables.data.map(deliverable => (
            <div
              className="cursor-pointer mb-3 overflow-x-hidden"
              key={deliverable.uid}
            >
              <CatalogCard
                data={deliverable}
                isSelected={deliverables.data.some(
                  d => d.uid === deliverable.uid,
                )}
                showActions={true}
                showDefinition={true}
              />
            </div>
          ))
        ) : (
          // TODO: Add empty state DS and apply here
          <NoResults />
        )}
        <DragOverlay>
          {draggedDeliverable && (
            <CatalogCard data={draggedDeliverable} isDragging />
          )}
        </DragOverlay>
      </div>
    </div>
  );
}
