import { OrderBy } from '~/shared/types/Sort';

export enum DeliverableType {
  KPI = 'KPI',
  PROJECT = 'PROJECT',
}
export interface CatalogFilterOptions {
  deliverableTypes?: string[];
  isActive?: boolean;
  businessFunctions?: string[];
  stagedBusinessFunctions?: string[];
  fuzzy_search?: string;
  orderBy?: OrderBy | null | undefined;
}

export type CatalogFilterProps = {
  setFilters: (filters: CatalogFilterOptions) => void;
  filters?: CatalogFilterOptions;
};
