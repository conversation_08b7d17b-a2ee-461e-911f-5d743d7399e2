import React from 'react';
import { Search, X } from 'react-bootstrap-icons';
import { Button, Input } from '@ghq-abi/design-system-v2';
import { useTranslate } from '@tolgee/react';

import { SortDropdown } from '~/shared/components/SortBy';

import { SearchBarProps } from './types';

export function SearchBar({
  searchQuery,
  onSearchChange,
  onSearchKeyPress,
  showSearchButton = false,
  showSortButton = false,
  handleSearchSubmit,
  placeholder,
  handleSortChange,
  orderBy,
}: SearchBarProps) {
  const { t } = useTranslate();

  return (
    <div className="flex w-full items-center gap-2">
      <div className="relative w-full">
        <Input
          placeholder={placeholder || t('common_search_by_name_id')}
          variant="filled"
          value={searchQuery}
          onChange={e => onSearchChange(e.target.value)}
          onKeyDown={onSearchKeyPress}
          className="flex-1"
          aria-label="Search catalog items"
        />
        {searchQuery && (
          <Button
            className="absolute top-1/2 right-3 -translate-y-1/2 p-1 hover:bg-gray-200 rounded-full w-4 h-4 bg-transparent"
            onClick={() => onSearchChange('')}
            type="button"
          >
            <X size={14} aria-hidden="true" />
          </Button>
        )}
      </div>
      {showSortButton && (
        <SortDropdown
          orderBy={orderBy}
          onSortOptionsChange={handleSortChange || (() => {})}
        />
      )}
      {showSearchButton && (
        <Button
          variant="secondary"
          type="button"
          border="default"
          size="icon"
          className="flex-shrink-0"
          onClick={handleSearchSubmit}
        >
          <Search size={14} aria-hidden="true" />
        </Button>
      )}
    </div>
  );
}
