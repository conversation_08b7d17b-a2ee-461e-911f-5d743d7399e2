import { OrderBy, SortOptions } from '~/shared/types/Sort';

export type SearchBarProps = {
  searchQuery: string;
  onSearchChange: (value: string) => void;
  onSearchKeyPress: (event: React.KeyboardEvent<HTMLInputElement>) => void;
  showSearchButton?: boolean;
  showSortButton?: boolean;
  handleSearchSubmit?: () => void;
  placeholder?: string;
  handleSortChange?: (sortOptions: SortOptions | null) => void;
  orderBy?: OrderBy | null | undefined;
};
