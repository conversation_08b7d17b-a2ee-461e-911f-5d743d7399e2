# NORTHSTAR MICROFRONTEND

## NORTHSTAR LEGACY SESSION

For using NorthStar with the full experience, we need to generate northstar session that is shared through Cookies.
But for users that have not accessed screens that are part of the "legacy frontend" we need to force a "northstar login"
by adding a call to {baseUrl}/api/auth/v2/sso/legacy/login (we use this route instead of {baseUrl}/api/auth/v2/sso/login because
the authentication on peopleplatform api has already happened by the ghq-people-platform-auth-lib, therefore, we don't need to
force it again).

## NORTHSTAR LEGACY PROXY

To make it possible to use the old northstar proxy on the new microfrontends, we had to force the proxy piece if it exists
on each redirect.
Also, to check the proxy url is valid, we call {baseUrl}/api/auth/v1/url/login.

Places we did this: src/app/components/HeaderActions/index.tsx, srx/app/components/Nav/hooks/useMenuRoutes.tsx, src/app/templates/App/index.tsx,
src/entities/CreateNewCatalogItem/hooks/index.tsx, src/shared/components/CatalogItemDetailDrawer/CatalogItemDetailDrawer.tsx, src/shared/services/auth.ts.

## MICROFRONTEND NEEDS

For using microfrontend with ghq-people-platform-auth-lib and GHQ_ABI_BOILERPLATE_WEBCLIENT we need to set
some environment variables and force the basePath in some places:

```properties
NEXT_PUBLIC_BASE_PATH={basePath}
NEXT_PUBLIC_APP_URL=https://example.ab-inbev.com // this will be {rootUrl}
NEXTAUTH_URL={rootUrl}{basePath} // this will be considered {baseUrl}
PUBLIC_PP_AUTH_LIB_AZURE_REDIRECT_URI={baseUrl}/api/auth/callback/azure-ad // if using another provider, change just the "azure-ad" piece
NEXT_PUBLIC_API_URL=https://example.ab-inbev.com/api/example-api // this will be {appApi}
NEXT_PUBLIC_NORTHSTAR_AUTH_API_URL=https://example.ab-inbev.com/api/auth // this will be {northstarAuthApi}
```

Oother places: src/app/styles/global.ts, src/pages/_document.tsx, all fetch calls (src/pages/_app.tsx, src/app/components/Nav/hooks/useMenuRoutes),
all router.push and callbacks (src/pages/auth/redirect.tsx, src/pages/_app.tsx, src/app/components/UserDropdown/useUserDropdown.tsx).

We need also to implement some code in the next.config.js:

```JS
  basePath: process.env.NEXT_PUBLIC_BASE_PATH, // redundancy of basePath to guarantee NextClient will understand this.
  assetPrefix: process.env.NEXT_PUBLIC_BASE_PATH, // tells next where to look for /assets
  publicRuntimeConfig: {
    basePath: process.env.NEXT_PUBLIC_BASE_PATH, // force this value on runtime
    assetPrefix: process.env.NEXT_PUBLIC_BASE_PATH, // force this value on runtime
  },
  env: {
    NEXT_PUBLIC_BASE_PATH: process.env.NEXT_PUBLIC_BASE_PATH, // force redundancy since when not set, next ignores some of them on production.
    NEXTAUTH_URL: process.env.NEXTAUTH_URL, // force redundancy since when not set, next ignores some of them on production.
    PUBLIC_PP_AUTH_LIB_AZURE_REDIRECT_URI:
      process.env.PUBLIC_PP_AUTH_LIB_AZURE_REDIRECT_URI, // force redundancy since when not set, next ignores some of them on production.
    NEXT_PUBLIC_NORTHSTAR_AUTH_API_URL:
      process.env.NEXT_PUBLIC_NORTHSTAR_AUTH_API_URL, // force redundancy since when not set, next ignores some of them on production.
    NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL, // force redundancy since when not set, next ignores some of them on production.
  },

  // this tells the deployed version where to look for statis files.
  async rewrites() {
    return [
      {
        source: `${process.env.NEXT_PUBLIC_BASE_PATH}/_next/:path*`,
        destination: '/_next/:path*',
      },
    ];
  },
```

### Mermaid

```mermaid
sequenceDiagram
    actor User
    actor Browser
    actor NextApi
    actor NextStatic
    actor NextClient
    actor AzureAD
    actor NorthstarAuthApi

    User->Browser: Acessa o microfront
    NextClient->AzureAD: Redireciona para o endpoint de autorização do Azure AD com parâmetros (client_id, redirect_uri, scope, etc.)
    AzureAD->Browser: Redireciona o navegador para a página de login do Azure AD
    User->AzureAD: Insere credenciais (email/senha) + 2º fator
    AzureAD->Browser: Redireciona para redirect_uri configurado na NextApp (/api/auth/callback/azure-ad) com code e state
    Browser->NextApi: Requisição para /api/auth/callback/azure-ad com code
    NextApi->AzureAD: Troca o code por Access Token e ID Token (via POST para o endpoint de token do Azure AD)
    AzureAD->NextClient: Retorna Access Token, ID Token e Refresh Token (opcional)
    Browser->NextApi: Requisição para {baseUrl}/api/auth/session
    Browser->NextClient: Requisição para {baseUrl}
    Browser->NextStatic: Requisição para os arquivos estáticos referenciados no index.html
    Browser->NextApi: Requisição para {baseUrl}/api/auth/old-cookies-handler
    Browser->NextApi: Requisição para {baseUrl}/api/auth/should-sign-in
    NextClient->Browser: Cria cookie de sessão do Next-Auth (JWT ou JWE) e redireciona para a página inicial/dashboad
    NextClient->NorthstarAuthApi: Busca usuário no banco de dados, vinculando-o ao ID do Azure AD
    NorthstarAuthApi->NextClient: Inicia a sessão e retorna o Cookies e CSRF
    Browser->User: Exibe página autenticada
```
