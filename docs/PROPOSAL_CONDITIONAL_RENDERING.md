# Documentação Simplificada - Renderização Condicional

## Visão Geral

Esta tela de proposta funciona como um **fluxo de trabalho colaborativo** entre SPOC (supervisor) e EMPLOYEE (funcionário), onde cada um tem permissões específicas em diferentes momentos do processo.

## Conceitos

### Status da Proposta
- **`NOT_STARTED`**: Proposta criada mas ainda não iniciada
- **`IN_PROGRESS_PROPOSAL`**: SPOC está criando a proposta inicial
- **`IN_PROGRESS_FEEDBACK`**: EMPLOYEE está fornecendo feedback
- **`IN_PROGRESS_FINAL`**: SPOC está criando a versão final
- **`COMPLETED`**: Processo concluído, todos podem visualizar

### Tipos de Usuário
- **SPOC**: Supervisor/Gerente (cria proposta e versão final)
- **EMPLOYEE**: <PERSON>cion<PERSON>rio (fornece feedback)

### Steps da Interface
- **Step 1**: Proposta inicial
- **Step 2**: Feedback do funcionário
- **Step 3**: Versão final
- **Step 4**: Comparação entre versões (só quando concluído)

## Badges de Status (Sempre Visíveis)

| Status | Badge | Cor | Ícone |
|--------|-------|-----|-------|
| `NOT_STARTED` | "Not Started" | 🔘 Cinza | NotStarted |
| `IN_PROGRESS_*` | "In Progress" | 🟡 Amarelo | InProgress |
| `COMPLETED` | "Completed" | 🟢 Verde | Check2 |

## Componentes por Situação

### DragNDrop (Edição Ativa)
**Quando aparece:**
- SPOC no Step 1 durante `IN_PROGRESS_PROPOSAL` → Criando proposta
- EMPLOYEE no Step 2 durante `IN_PROGRESS_FEEDBACK` → Criando feedback
- SPOC no Step 3 durante `IN_PROGRESS_FINAL` → Criando versão final

### TabEmptyState (Bloqueado/Aguardando)
**Quando aparece:**
- Usuário sem visibilidade na fase atual
- Aguardando ação do outro usuário
- Conteúdo ainda não disponível

**Mensagens típicas:**
- "No proposals available" (Employee sem proposal)
- "Nothing to see here yet" (Aguardando preenchimento)

### 📋 Steps (Visualização Normal)
- Proposal Step, Feedback Step, Final Step

### 📄 TargetCard List (Revisão sem Edição)
**Quando aparece:**
- SPOC revisando targets criados pelo EMPLOYEE
- Modo read-only com detalhes dos targets

## Tabela Completa de Renderização

### Status: NOT_STARTED

| Usuário | Step 1 | Step 2 | Step 3 | Step 4 |
|---------|--------|--------|--------|--------|
| **SPOC** | 📋 Proposal Step | 🚫 Empty State | 🚫 Empty State | ❌ |
| **EMPLOYEE** | 🚫 Empty State | 🚫 Empty State | 🚫 Empty State | ❌ |

### Status: IN_PROGRESS_PROPOSAL

| Usuário | Step 1 | Step 2 | Step 3 | Step 4 |
|---------|--------|--------|--------|--------|
| **SPOC** | ✏️ **DragNDrop** | 🚫 TabEmptyState | 🚫 Empty State | ❌ |
| **EMPLOYEE** | 🚫 TabEmptyState | 🚫 Empty State | 🚫 Empty State | ❌ |

### Status: IN_PROGRESS_FEEDBACK

| Usuário | Step 1 | Step 2 | Step 3 | Step 4 |
|---------|--------|--------|--------|--------|
| **SPOC** | 📄 Target Card List | 🚫 Empty State | 🚫 Empty State | ❌ |
| **EMPLOYEE** | 📋 Proposal Step | ✏️ **DragNDrop** | 🚫 Empty State | ❌ |

### Status: IN_PROGRESS_FINAL

| Usuário | Step 1 | Step 2 | Step 3 | Step 4 |
|---------|--------|--------|--------|--------|
| **SPOC** | 📋 Proposal Step | 📄 Target Card List | ✏️ **DragNDrop** | ❌ |
| **EMPLOYEE** | 📋 Proposal Step | 📋 Feedback Step | 🚫 Empty State | ❌ |

### Status: COMPLETED

| Usuário | Step 1 | Step 2 | Step 3 | Step 4 |
|---------|--------|--------|--------|--------|
| **SPOC** | 📋 ProposalStep | 📋 FeedbackStep | 📋 FinalStep | 📊 ComparativeStep |
| **EMPLOYEE** | 📋 ProposalStep | 📋 FeedbackStep | 📋 FinalStep | 📊 ComparativeStep |

## Filtros de Targets

Os targets são automaticamente filtrados por tipo:

| Tipo de Target | Usado em | Descrição |
|---------------|----------|-----------|
| **PROPOSAL** | Steps 1 e 4 | Targets relacionados à proposal inicial |
| **FEEDBACK** | Steps 2 e 4 | Targets relacionados ao feedback |
| **FINAL** | Steps 3 e Complete | Targets relacionados ao final proposal |

1. **Fase 1**: SPOC cria proposta (Step 1) → Status: `IN_PROGRESS_PROPOSAL`
2. **Fase 2**: EMPLOYEE dá feedback (Step 2) → Status: `IN_PROGRESS_FEEDBACK`
3. **Fase 3**: SPOC cria versão final (Step 3) → Status: `IN_PROGRESS_FINAL`
4. **Fase 4**: Todos podem comparar → Status: `COMPLETED`
