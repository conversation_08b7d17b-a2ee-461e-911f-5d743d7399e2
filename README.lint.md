# 🧹 Padrões de Lint e Formatação

Projeto configurado para manter um padrão consistente de código, usando **Prettier**, **EditorConfig** e configurações específicas do **VS Code**.

---

## 📁 Estrutura de Arquivos

```json
your-project/
├── .vscode/
│ └── settings.json  ← Força o VS Code a usar Prettier + ESLint
├── .prettierrc      ← Configura o estilo de formatação do código
├── .editorconfig    ← Define regras de indentação e quebra de linha
```

---

## ⚙️ Configurações

### `.vscode/settings.json`

```json
{
  "editor.formatOnSave": true,
  "editor.formatOnPaste": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "[javascript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit"
  },
  "formattingToggle.affects": ["formatOnSave"]
}
```

- **editor.defaultFormatter**: define Prettier como o formatador padrão.

- **editor.formatOnSave**: formata o código automaticamente ao salvar.

- **editor.formatOnPaste**: formata automaticamente ao colar código.

- **codeActionsOnSave**: aplica correções do ESLint ao salvar.

### `.prettierrc`

```json
{
  "semi": true,
  "trailingComma": "all",
  "singleQuote": true,
  "printWidth": 80,
  "arrowParens": "avoid"
}
```

- **semi**: true → Adiciona ponto e vírgula no final das linhas.

- **trailingComma**: all → Adiciona vírgula no último item de arrays/objetos.

- **singleQuote**: true → Usa aspas simples.

- **printWidth**: 80 → Limita linhas a 80 caracteres.

- **arrowParens**: avoid → Remove parênteses em arrow functions com 1 parâmetro.

### `.editorconfig`

### EditorConfig is awesome: https://EditorConfig.org

```json
root = true

[*]
indent_style = space
indent_size = 2
end_of_line = lf
charset = utf-8
trim_trailing_whitespace = true
insert_final_newline = true
```

- **indent_style**: space → Usa espaços (e não tabs).

- **indent_size**: 2 → Dois espaços por nível.

- **end_of_line**: lf → Quebra de linha estilo Unix.

- **charset**: utf-8 → Codificação padrão universal.

- **trim_trailing_whitespace**: true → Remove espaços no fim de linhas.

- **insert_final_newline**: true → Garante uma linha em branco ao final dos arquivos.
