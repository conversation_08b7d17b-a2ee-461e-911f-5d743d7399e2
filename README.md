# WebClient Boilerplate

This repository provides a boilerplate for building web client applications using Next.js. It includes essential configurations, utilities, and best practices to streamline development and ensure consistency across projects.

## Table of Contents

- [Getting Started](#getting-started)
- [CI/CD Pipeline Customization](#cicd-pipeline-customization)
- [Folder Structure](#folder-structure)
- [Key Features](#key-features)
- [Authentication](#authentication)
- [Error Handling](#error-handling)
- [Server-Side Rendering (SSR)](#server-side-rendering-ssr)
- [Middleware](#middleware)
- [Environment Variables](#environment-variables)
- [Development Guidelines](#development-guidelines)
- [Testing](#testing)
- [Deployment](#deployment)

---

## Getting Started

### Prerequisites

Ensure you have the following installed:

- Node.js (v20)
- npm

### Installation

1. Clone the repository:

   ```bash
   git clone <repository-url>
   cd webclient
   ```

2. Install dependencies:

   ```bash
   npm install
   ```

3. Start the development server:

   ```bash
   npm run dev
   ```

4. Open the application in your browser at `http://localhost:3000`.

---

## CI/CD Pipeline Customization

When creating a new project from this boilerplate, you must adjust the CI/CD pipeline configuration to reflect your new project's context. The main changes to make in the `pipelines/azure-pipeline.yml` file include:

1. **Project and Application Name**

   - Update the `projectName` and `appName` variables to match your new project's name.

2. **Domains and URLs**

   - Change the `appUrl` variables to the domain of your new project.

3. **Secrets and Tokens**

   - Update `secretVolumeToken`, `stellarWidgetConnectorId`, `stellarWidgetTemplateId`, and other credentials to the values for your new environment.

4. **Environment Variables**

   - Review and adjust all environment variables required for your new project's operation.

5. **Azure Key Vault Secrets**

   - Change `AKV_URI` and `SigningKeyName` to the values for your new project, if applicable.

6. **Other Adjustments**
   - Review custom steps, artifact names, and file paths according to your new project's structure.

> **Tip:** Always validate the pipeline after making changes by running a test build before deploying to production.

---

## Folder Structure

The repository is organized as follows:

```
.
├── src/
│   ├── app/
│   │   ├── components/          # Reusable React components
│   │   ├── contexts/            # Context API for global state management
│   │   ├── hocs/                # Higher-order components for SSR and authentication
│   │   └── pages/               # Next.js pages
│   ├── entities/                # Domain-specific entities
│   ├── shared/                  # Shared utilities, constants, and components
│   └── styles/                  # Global and component-specific styles
├── public/                      # Static assets (images, fonts, etc.)
├── pipelines/                   # CI/CD pipeline configurations
├── .env                         # Environment variables
└── README.md                    # Documentation
```

---

## Key Features

### 1. **Authentication**

- Uses `next-auth` for authentication.
- Key files:
  - `src/pages/api/auth/[...nextauth].ts`: Handles authentication routes.
  - `src/app/hocs/withSSRSession.ts`: Ensures authenticated server-side rendering.
  - `src/app/hocs/withSSRUnauth.ts`: Redirects authenticated users away from unauthenticated pages.

### 2. **Error Handling**

- Centralized error handling using an `ErrorBoundary` component.
- Key file:
  - `src/app/components/ErrorBoundary/index.tsx`

### 3. **Server-Side Rendering (SSR)**

- Utilities for SSR handle session management and device detection.
- Key files:
  - `src/app/hocs/withSSRSession.ts`
  - `src/app/hocs/withSSRUnauth.ts`

### 4. **Middleware**

- Custom middleware for request handling and authentication.
- Key file:
  - `src/middleware.ts`

### 5. **Permissions and Role Management**

- Implements role-based access control using `@casl/ability`.
- Key files:
  - `src/shared/auth/permissions.ts`
  - `src/app/contexts/AbilityContext.tsx`

---

## Authentication

### Setting Up Authentication

1. Configure `next-auth` options in `src/pages/api/auth/[...nextauth].ts`.
2. Use `withSSRSession` for pages requiring authentication:

   ```ts
   export const getServerSideProps = withSSRSession(
     async (context, session) => {
       // Your logic here
     }
   );
   ```

3. Use `withSSRUnauth` for pages that should not be accessible by authenticated users:
   ```ts
   export const getServerSideProps = withSSRUnauth(async context => {
     // Your logic here
   });
   ```

---

## Error Handling

- Wrap your application with the `ErrorBoundary` component to catch and display errors gracefully:

  ```tsx
  import ErrorBoundary from '../components/ErrorBoundary';

  function App() {
    return (
      <ErrorBoundary>
        <YourApp />
      </ErrorBoundary>
    );
  }
  ```

---

## Server-Side Rendering (SSR)

- **Detect Mobile Devices**: The `checkIfIsMobileDevice` utility in `withSSRSession.ts` detects if the user is on a mobile device.
- **Session Management**: The `getServerSession` function retrieves the user's session for SSR.

---

## Middleware

- Middleware is used for request handling and authentication.
- Key file: `src/middleware.ts`
- Example:
  ```ts
  export async function middleware(req, res) {
    // Your middleware logic here
  }
  ```

---

## Environment Variables

- Configure environment variables in the `.env` file.
- Example:
  ```env
  NEXTAUTH_URL=http://localhost:3000
  NEXTAUTH_SECRET=your-secret-key
  ```

---

## Development Guidelines

1. **Code Style**: Follow the ESLint and Prettier configurations provided in the repository.
2. **Testing**: Write unit tests for all components and utilities.
3. **Commits**: Use meaningful commit messages following the [Conventional Commits](https://www.conventionalcommits.org/) standard.

---

## Testing

- Run tests using Jest:
  ```bash
  npm run test
  ```
- Watch mode:
  ```bash
  npm run test:watch
  ```
- CI mode:
  ```bash
  npm run test:ci
  ```

---

## Deployment

- Deployment configurations are located in the `pipelines/manifests/` directory.
- Example files:
  - `deployment.yml`: Deployment configuration.
  - `ingress.yml`: Ingress configuration.
  - `service.yml`: Service configuration.

---

## Additional Resources

- [Next.js Documentation](https://nextjs.org/docs)
- [NextAuth.js Documentation](https://next-auth.js.org/getting-started/introduction)
- [CASL Documentation](https://casl.js.org/)

For further assistance, contact the repository maintainers.
