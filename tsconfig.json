{"compilerOptions": {"baseUrl": "src", "target": "es6", "sourceMap": true, "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "paths": {"~/*": ["*"]}, "plugins": [{"name": "next"}]}, "include": ["next.config.js", "lint-staged.config.js", "jest.config.js", ".eslintrc.js", ".jest/setup.ts", "next-env.d.ts", "**/*.ts", "**/*.d.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}