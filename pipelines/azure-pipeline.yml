# prettier-ignore

trigger:
  branches:
    include:
      - dev
      - staging
      - main
pool:
  vmImage: ubuntu-latest

resources:
  - repo: self

variables:
  - ${{ if and(ne(variables['Build.SourceBranchName'], 'main'), ne(variables['Build.SourceBranchName'], 'staging')) }}:
    - group: tsc-dev
    - name: k8sNamespaceIngress
      value: people-product-dev
    - name: k8srvconIngress
      value: 'peopleproducts-aks-gb-dev-people-product-dev'
    - name: publicGaId
      value: 'G-KWWEB4EM5J'
    - name: appHost
      value: 'tsc-dev.ab-inbev.com'
    - name: appUrl
      value: 'https://$(appHost)'
    - name: lcmUrl
      value: 'https://dev.180-360.ab-inbev.com'
    - name: oprUrl
      value: 'https://dev.opr.ab-inbev.com'
    - name: northStarUrl
      value: 'https://tsc-dev.ab-inbev.com'
    - name: rewardsUrl
      value: 'https://test.rewards.ab-inbev.com'
    - name: metricsUrl
      value: 'https://dev.metrics.ab-inbev.com/plugin/bundle.js'
    - name: cheershubUrl
      value: 'https://abinbevwwqa.service-now.com/cheers'
    - name: stellarWidgetUrl
      value: 'https://stellarwdigetpeopledev.z13.web.core.windows.net/init.js'
    - name: secretVolumeToken
      value: 'service-account-people-produts-token-x4l7l'
    - name: replicas
      value: 1
    - name: stellarWidgetConnectorId
      value: '281fed78-11f2-46a1-9838-10734cda8576'
    - name: stellarWidgetTemplateId
      value: '5f4fb002-c082-4946-8c9a-d5159ac3fa0f'
    - name: stellarWidgetEnabled
      value: 'true'
    - name: nextAuthSessionExpInSeconds
      value: '604800'
  - ${{ if eq(variables['Build.SourceBranchName'], 'staging') }}:
    - group: tsc-qa
    - name: k8sNamespaceIngress
      value: people-product-stg
    - name: k8srvconIngress
      value: 'peopleproducts-aks-gb-dev-people-product-stg'
    - name: publicGaId
      value: 'G-SS428DP9GB'
    - name: appHost
      value: 'tsc-qa.ab-inbev.com'
    - name: appUrl
      value: 'https://$(appHost)'
    - name: lcmUrl
      value: 'https://test.180-360.ab-inbev.com'
    - name: oprUrl
      value: 'https://test.opr.ab-inbev.com'
    - name: northStarUrl
      value: 'https://tsc-qa.ab-inbev.com'
    - name: rewardsUrl
      value: 'https://test.rewards.ab-inbev.com'
    - name: metricsUrl
      value: 'https://test.metrics.ab-inbev.com/plugin/bundle.js'
    - name: cheershubUrl
      value: 'https://abinbevwwqa.service-now.com/cheers'
    - name: stellarWidgetUrl
      value: 'https://stellarwidgetpeopleuat.z20.web.core.windows.net/init.js'
    - name: secretVolumeToken
      value: 'service-account-people-produts-token-vlf7q'
    - name: replicas
      value: 1
    - name: stellarWidgetConnectorId
      value: '71c8e101-5aaf-437d-8c5d-10998711b016'
    - name: stellarWidgetTemplateId
      value: 'eef84932-d1d7-4dbb-9c43-35338cd882ca'
    - name: stellarWidgetEnabled
      value: 'true'
    - name: nextAuthSessionExpInSeconds
      value: '604800'
  - ${{ if eq(variables['Build.SourceBranchName'], 'main') }}:
    - group: tsc-prod
    - name: k8sNamespaceIngress
      value: people-product-prod
    - name: k8srvconIngress
      value: 'peopleproducts-aks-gb-dev-people-product-prod'
    - name: publicGaId
      value: 'G-PKWGF2577F'
    - name: appHost
      value: 'northstar.ab-inbev.com'
    - name: appUrl
      value: 'https://$(appHost)'
    - name: lcmUrl
      value: 'https://180-360.ab-inbev.com'
    - name: oprUrl
      value: 'https://opr.ab-inbev.com'
    - name: northStarUrl
      value: 'https://northstar.ab-inbev.com'
    - name: rewardsUrl
      value: 'https://rewards.ab-inbev.com'
    - name: metricsUrl
      value: 'https://metrics.ab-inbev.com/plugin/bundle.js'
    - name: cheershubUrl
      value: 'https://abinbevww.service-now.com/cheers'
    - name: secretVolumeToken
      value: 'service-account-people-produts-token-nc25x'
    - name: replicas
      value: 5
    - name: stellarWidgetUrl
      value: ''
    - name: stellarWidgetConnectorId
      value: ''
    - name: stellarWidgetTemplateId
      value: ''
    - name: stellarWidgetEnabled
      value: 'false'
    - name: nextAuthSessionExpInSeconds
      value: '604800'
  - name: appName
    value: 'tsc-catchball-webclient'
  - name: dockerRegistryServiceConnection
    value: '************************************'
  - name: imageRepository
    value: $(appName)
  - name: containerRegistry
    value: 'peopleproductsacr.azurecr.io'
  - name: dockerfilePath
    value: $(Build.SourcesDirectory)/Dockerfile
  - name: tag
    value: '$(Build.BuildId)'
  - name: vmImageName
    value: 'ubuntu-latest'
  - name: AKV_URI
    value: 'peopleplatform.vault.azure.net'
  - name: SigningKeyName
    value: 'container-image-signing-key'
  - name: IMAGE_DIGEST
    value: ''
  - name: appsec-akv-sp-clientid
    value: 'aa52a4de-800c-499d-bb36-7cef858a1d3b'
  - name: basePath
    value: '/catchball'

stages:
  - stage: Build
    displayName: Build and push stage
    jobs:
      - job: Build
        workspace:
          clean: all
        displayName: Build
        pool:
          vmImage: $(vmImageName)
        steps:
          - task: NodeTool@0
            inputs:
              versionSpec: '20.x'
            displayName: 'Install Node.js'

          - task: Npm@1
            inputs:
              command: ci
              workingFile: '$(Build.Repository.Name)/.npmrc'
            displayName: Install dependencies

          - task: Npm@1
            inputs:
              command: 'custom'
              customCommand: 'run test:ci'
            displayName: Run tests

          - script: |
              curl -sL https://github.com/sigstore/cosign/releases/latest/download/cosign-linux-amd64 --output cosign
              chmod +x cosign
              mv cosign /usr/local/bin/
            displayName: 'Install Cosign'

          - task: Docker@2
            displayName: Build and push image to container registry
            inputs:
              command: buildAndPush
              repository: $(imageRepository)
              Dockerfile: $(dockerfilePath)
              containerRegistry: $(dockerRegistryServiceConnection)
              tags: |
                $(tag)
                latest

          ### Sign image

          - script: |
              curl -sL https://github.com/sigstore/cosign/releases/latest/download/cosign-linux-amd64 --output cosign
              chmod +x cosign
              mv cosign /usr/local/bin/
            displayName: 'Install Cosign'
          - task: AzureCLI@2
            inputs:
              azureSubscription: 'AzureNonProd'
              scriptType: 'bash'
              scriptLocation: 'inlineScript'
              inlineScript: |
                az acr login -n $(containerRegistry)
                echo "##vso[task.setvariable variable=IMAGE_DIGEST]$(az acr repository show --name $(containerRegistry) --image $(imageRepository):$(tag) --query digest --output tsv)"
            displayName: 'Generate session for cosign to communicate with ACR and obtain digest'

          - script: |
                cosign sign --key azurekms://$(AKV_URI)/$(SigningKeyName) --tlog-upload=false $(containerRegistry)/$(imageRepository)@$(IMAGE_DIGEST)
            env:
                AZURE_TENANT_ID: cef04b19-7776-4a94-b89b-375c77a8f936
                AZURE_CLIENT_ID: $(appsec-akv-sp-clientid)
                AZURE_CLIENT_SECRET: $(appsec-akv-sp-password)
            displayName: 'Sign a container image and store the signature in the registry'

          - script: |
                  cosign verify --key azurekms://$(AKV_URI)/$(SigningKeyName) --insecure-ignore-tlog $(containerRegistry)/$(imageRepository):$(tag)
            env:
              AZURE_TENANT_ID: cef04b19-7776-4a94-b89b-375c77a8f936
              AZURE_CLIENT_ID: $(appsec-akv-sp-clientid)
              AZURE_CLIENT_SECRET: $(appsec-akv-sp-password)
            displayName: 'Validate a container image is signed with the right key'

            ### End of image sign

          - task: PublishPipelineArtifact@1
            displayName: 'Publish manifests artifact'
            inputs:
              artifactName: 'manifests'
              targetPath: $(Build.SourcesDirectory)

  - stage: CD
    displayName: Deploy stage
    condition: and(succeeded(), ne(variables['Build.Reason'], 'PullRequest'))
    jobs:
      - deployment: deployment
        displayName: Select environment
        environment: $(environmentName)

      - job: Deploy
        displayName: Deploy
        pool:
          vmImage: $(vmImageName)
        steps:
          - task: replacetokens@5
            inputs:
              rootDirectory: 'pipelines/manifests'
              targetFiles: '*.yml'
              tokenPattern: 'azpipelines'
              enableTelemetry: false
          - task: KubernetesManifest@0
            displayName: Deploy TSC
            inputs:
              action: deploy
              kubernetesServiceConnection: $(K8srvcon)
              namespace: $(k8sNamespace)
              manifests: |
                pipelines/manifests/deployment.yml
                pipelines/manifests/service.yml
              containers: $(containerRegistry)/$(imageRepository):$(tag)
          - task: KubernetesManifest@0
            displayName: Deploy People Product
            inputs:
              action: deploy
              kubernetesServiceConnection: $(K8srvconIngress)
              namespace: $(k8sNamespaceIngress)
              manifests: pipelines/manifests/ingress.yml
          - task: Kubernetes@1
            displayName: image update
            inputs:
              connectionType: 'Kubernetes Service Connection'
              kubernetesServiceEndpoint: $(K8srvcon)
              namespace: $(k8sNamespace)
              command: 'set'
              arguments: 'image deployment/$(appName) $(appName)=$(containerRegistry)/$(imageRepository):$(tag)'
              secretType: 'dockerRegistry'
              containerRegistryType: 'Azure Container Registry'
