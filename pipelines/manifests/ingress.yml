apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: abi-tsc-people-platform-api
  namespace: $(k8sNamespaceIngress)
  labels:
    k8s-app: abi-tsc-people-platform-api
  annotations:
    kubernetes.io/ingress.class: nginx
    kubernetes.io/proxy-body-size: 15m
    kubernetes.io/proxy-connect-timeout: '2400'
    kubernetes.io/proxy-read-timeout: '2400'
    kubernetes.io/proxy-send-timeouT: '2400'
    nginx.org/client-max-body-size: 15m
    nginx.org/server-snippets: gzip on;
spec:
  tls:
    - hosts:
        - $(appHost)
      secretName: tsc-tls
  rules:
    - host: $(appHost)
      http:
        paths:
          - path: /api/people-platform
            pathType: Prefix
            backend:
              service:
                name: people-platform-api
                port:
                  number: 80
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: abi-tsc-people-photo-api
  namespace: $(k8sNamespaceIngress)
  labels:
    k8s-app: abi-tsc-people-photo-api
  annotations:
    kubernetes.io/ingress.class: nginx
    kubernetes.io/proxy-body-size: 15m
    kubernetes.io/proxy-connect-timeout: '2400'
    kubernetes.io/proxy-read-timeout: '2400'
    kubernetes.io/proxy-send-timeouT: '2400'
    nginx.org/client-max-body-size: 15m
    nginx.org/server-snippets: gzip on;
spec:
  tls:
    - hosts:
        - $(appHost)
      secretName: tsc-tls
  rules:
    - host: $(appHost)
      http:
        paths:
          - path: /api/people-platform/photo
            pathType: Prefix
            backend:
              service:
                name: photo-api
                port:
                  number: 80
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: abi-tsc-people-auth-api
  namespace: $(k8sNamespaceIngress)
  labels:
    k8s-app: abi-tsc-people-auth-api
  annotations:
    kubernetes.io/ingress.class: nginx
    kubernetes.io/proxy-body-size: 15m
    kubernetes.io/proxy-connect-timeout: '2400'
    kubernetes.io/proxy-read-timeout: '2400'
    kubernetes.io/proxy-send-timeouT: '2400'
    nginx.org/client-max-body-size: 15m
    nginx.org/server-snippets: gzip on;
spec:
  tls:
    - hosts:
        - $(appHost)
      secretName: tsc-tls
  rules:
    - host: $(appHost)
      http:
        paths:
          - path: /api/auth-people-platform
            pathType: Prefix
            backend:
              service:
                name: auth-api
                port:
                  number: 80
